import React, { useEffect, useState } from 'react';
import {
    Alert,
    Pressable,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import CollapsibleView from './CollapsibleView';
import { Icon, MyText } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import DropdownMenu from './DropdownMenu';
import TextField from './TextField';
import { dateHelper, helper } from '@common';

const ReceiptItem = ({
    index,
    item,
    isCheckItem,
    openReceipt,
    onCheckList,
    // onPressDelete,
    onPressPrint
}) => {
    const {
        sendBankId,
        sendType,
        sendBankType,
        sendDate,
        sendMoney,
        bankName,
        sendUser,
        inOutVoucherId,
        depositUserFullName,
        approveReqStatusName,
        approveRequestId,
        inputTime
    } = item;
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isCheck, setIsCheck] = useState(false);

    useEffect(() => {
        setIsCollapsed(isCheckItem);
        setIsCheck(isCheckItem);
    }, [isCheckItem]);

    const menuItems = [
        {
            isCode: 1,
            title: 'Xem / Chỉnh sửa phiếu',
            iconName: 'documents',
            iconSet: 'Ionicons',
            color: COLORS.icF89000,
            disabled: false
        },
        {
            isCode: 3,
            title: 'In giấy nộp tiền',
            iconName: 'printer',
            iconSet: 'MaterialCommunityIcons',
            color: COLORS.ic218DEB,
            disabled: false
        }
    ];
    const menuItem = [
        {
            isCode: 3,
            title: 'In giấy nộp tiền',
            iconName: 'printer',
            iconSet: 'MaterialCommunityIcons',
            color: COLORS.ic218DEB,
            disabled: false
        }
    ];
    const listSendType = {
        1: 'Siêu thị tự đi nộp tiền',
        2: 'Ngân hàng đến thu tại siêu thị',
        3: 'Siêu thị nộp tiền nội bộ'
    };
    const statusColors = {
        'Khởi tạo': '#6600FF',
        'Chờ xử lý': '#FF9900',
        'Đã duyệt': '#00CC00',
        'Đã hủy': '#FF0000'
    };

    const parts = sendBankType.split(' - ');

    const onUpdateReceipt = () => {
        openReceipt(item);
    };

    const onPrintReceipt = () => {
        onPressPrint(item);
    };

    const handelPressItem = (title, isCode) => {
        switch (isCode) {
            case 1:
                onUpdateReceipt();
                break;
            // case 2:
            //     onDeleteReceipt();
            //     break;
            case 3:
                onPrintReceipt();
                break;
            default:
                break;
        }
    };

    const onCheck = () => {
        setIsCheck(!isCheck);
        onCheckList(!isCheck, index, item);
    };

    useEffect(() => {
        setIsCheck(false);
    }, [item]);

    return (
        <TouchableOpacity
            onPress={() => onCheck()}
            activeOpacity={0.8}
            style={[
                styles.container,
                {
                    backgroundColor: COLORS.bgFFFFFF,
                    borderColor: isCheck ? COLORS.bd0099E5 : COLORS.bgFFFFFF,
                    borderWidth: isCheck ? 1 : 0
                }
            ]}>
            <View style={styles.wrapper}>
                <View style={styles.title}>
                    <View>
                        <Icon
                            iconSet={'Ionicons'}
                            name={
                                isCheck ? 'checkbox-outline' : 'square-outline'
                            }
                            color={isCheck ? COLORS.ic147EFB : COLORS.ic333333}
                            size={20}
                        />
                    </View>
                    <MyText
                        text={'Phiếu: '}
                        style={[styles.header]}
                        children={
                            <MyText style={[styles.header]} text={sendBankId} />
                        }
                    />
                </View>
                {!!approveReqStatusName && (
                    <View
                        style={{
                            width: constants.width / 4
                        }}>
                        <MyText
                            style={[
                                styles.status,
                                {
                                    color: statusColors[approveReqStatusName]
                                }
                            ]}
                            text={approveReqStatusName}
                        />
                    </View>
                )}
                <DropdownMenu
                    menu={
                        approveReqStatusName == 'Đã hủy' ||
                        approveReqStatusName == null
                            ? menuItems
                            : menuItem
                    }
                    onPressItem={(title, isCode) =>
                        handelPressItem(title, isCode)
                    }
                />
            </View>
            <View style={styles.content}>
                <TextField label={'Loại nộp tiền'} value={parts[1]} />
                <TextField
                    label={'Thu ngân nộp tiền'}
                    value={`${sendUser} - ${depositUserFullName}`}
                />
                <TextField label={'Ngân hàng nộp tiền'} value={bankName} />
                <TextField
                    label={'Số tiền nộp'}
                    value={helper.convertNum(sendMoney)}
                    styleValue={styles.txMonney}
                />
                <TextField
                    label={'Ngày nộp'}
                    value={dateHelper.formatDateFULL(new Date(sendDate))}
                />
            </View>
            <CollapsibleView isCollapsed={!isCollapsed}>
                <View style={styles.content}>
                    <TextField
                        label={'Phương thức nộp tiền'}
                        value={listSendType[sendType]}
                    />
                    <TextField
                        label={'Mã yêu cầu duyệt chi'}
                        value={approveRequestId}
                    />
                    <TextField label={'Mã phiếu chi'} value={inOutVoucherId} />
                    <TextField
                        label={'Ngày chi'}
                        value={
                            inputTime
                                ? dateHelper.formatDateFULL(new Date(inputTime))
                                : ''
                        }
                    />
                </View>
            </CollapsibleView>
            <Pressable
                style={styles.btnSeeMore}
                onPress={() => setIsCollapsed(!isCollapsed)}>
                <Icon
                    name={
                        isCollapsed
                            ? 'chevron-up-circle-outline'
                            : 'chevron-down-circle-outline'
                    }
                    iconSet={'Ionicons'}
                    size={15}
                    color={COLORS.bg147EFB}
                />
                <MyText
                    style={[
                        styles.header,
                        {
                            color: COLORS.txt037EF3,
                            fontWeight: '600',
                            fontSize: 12,
                            textAlign: 'center'
                        }
                    ]}
                    text={isCollapsed ? 'Thu gọn' : 'Xem chi tiết'}
                />
            </Pressable>
        </TouchableOpacity>
    );
};

export default ReceiptItem;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginHorizontal: 10,
        borderRadius: 8
    },
    wrapper: {
        padding: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    title: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10
    },
    content: {
        width: constants.width - 20,
        paddingBottom: 10,
        paddingHorizontal: 35,
        gap: 10
    },
    header: {
        fontWeight: '600',
        fontSize: 15
    },
    item: {
        alignItems: 'center',
        flexDirection: 'row',
        gap: 10,
        paddingHorizontal: 10,
        paddingTop: 5
    },
    txLabel: {
        fontWeight: '600'
    },
    txMonney: {
        color: COLORS.txtD0021B
    },
    status: {
        fontSize: 12.5,
        fontWeight: '600'
    },
    btnSeeMore: {
        width: constants.getSize(100),
        padding: 8,
        alignSelf: 'flex-end',
        alignItems: 'center',
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'center'
    }
});
