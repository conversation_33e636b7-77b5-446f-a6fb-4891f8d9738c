import React, { useState } from 'react';
import {
    StyleSheet,
    TextInput,
    View,
    Alert,
    SafeAreaView,
    Keyboard
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import {
    <PERSON><PERSON>,
    CaptureCamera,
    DatePicker,
    hideBlockUI,
    MyText,
    showBlock<PERSON>
} from '@components';
import { COLORS } from '@styles';
import { API_CONST, ENUM, constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { getImageCDN } from '../../action';
import ImageAdjust from '../components/ImageAdjust';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as sendBankActionCreator from '../../action';

const { API_GET_IMAGE_CDN } = API_CONST;

const CreateRequest = ({
    navigation,
    sendBankAction,
    dataSearchUser,
    route
}) => {
    const lstSendBank = route.params;
    const totalSendMoney = lstSendBank.reduce(
        (sum, item) => sum + item.sendMoney,
        0
    );
    const [totalMoney, setTotalMoney] = useState(totalSendMoney);
    const [userId, setUserId] = useState('');
    const [userName, setUserName] = useState('');
    const [date, setDate] = useState(new Date());
    const [uriImages, setUriImages] = useState([]);
    const [indexSelected, setIndexSelected] = useState(0);
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);

    const dataSearch = dataSearchUser[0];
    const [maxDate, setMaxDate] = useState(new Date());
    const [minDate, setMinDate] = useState(new Date());

    const onSelectDate = (date) => {
        setDate(new Date(date));
    };

    const onChangeTxUser = (text) => {
        let newValue = text.replace(/\D/g, ''); // Loại bỏ mọi ký tự không phải số
        if (newValue.startsWith('0')) {
            newValue = newValue.slice(1);
        } // Xóa số 0 đầu nếu có
        setUserId(newValue);
    };

    const onChangeUser = (text) => {
        Keyboard.dismiss();
        let newValue = text.replace(/\D/g, ''); // Loại bỏ mọi ký tự không phải số
        if (newValue.startsWith('0')) {
            newValue = newValue.slice(1); // Xóa số 0 đầu nếu có
        } else {
            sendBankAction
                .searchUser(text)
                .then((response) => {
                    const fullName = response[0].fullname;
                    setUserName(fullName);
                    hideBlockUI();
                })
                .catch((error) => {
                    hideBlockUI();
                    Alert.alert('Thông báo', error.msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                setUserName('');
                                setUserId('');
                                hideBlockUI();
                            }
                        }
                    ]);
                });
        }
    };

    const onCreateApprove = () => {
        showBlockUI();
        let dataImages = uriImages.map((uri) => ({ data: uri }));
        const body = {
            realSendDate: date,
            approveUser: userId,
            totalMoney: totalMoney,
            lstSendBank: lstSendBank,
            lstFile: dataImages
        };
        sendBankAction
            .createSendBankApprove(body)
            .then((response) => {
                Alert.alert(
                    translate('common.notification'),
                    'Tạo phiếu duyệt chi thành công',
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: () => {
                                setUserName('');
                                setUserId('');
                                setUriImages([]);
                                setTotalMoney('');
                                setDate(new Date());
                                setIsVisibleCamera(false);
                                hideBlockUI();
                                sendBankAction.getListDepositReceipt();
                                navigation.goBack();
                            }
                        }
                    ]
                );
            })
            .catch((error) => {
                Alert.alert(translate('common.notification'), error, [
                    {
                        text: translate('common.btn_accept'),
                        onPress: () => {
                            setUserName('');
                            setUserId('');
                            hideBlockUI();
                        }
                    }
                ]);
            });
    };

    const onSearchReceipt = () => {
        if (totalMoney == '') {
            Alert.alert(
                translate('common.notification'),
                translate('sendbank.please_enter_total_money')
            );
            return;
        } else if (userId == '') {
            Alert.alert(
                translate('common.notification'),
                translate('sendbank.please_enter_user_name')
            );
            return;
        } else if (uriImages.length == 0) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng thêm danh sách đính kèm'
            );
            return;
        } else {
            Alert.alert(
                translate('common.notification'),
                `Bạn xác nhận tạo phiếu yêu cầu duyệt chi số tiền nộp ngân hàng ${helper.formatMoney(
                    totalMoney
                )} cho QL/TC ${userId} - ${userName} duyệt?`,
                [
                    {
                        text: translate('common.btn_cancel'),
                        onPress: () => {
                            console.log('Cancel Pressed');
                        }
                    },
                    {
                        text: translate('common.btn_accept'),
                        onPress: () => {
                            onCreateApprove();
                        },
                        style: 'default'
                    }
                ]
            );
            return;
        }
    };

    const openCamera = (index) => () => {
        setIsVisibleCamera(true);
        setIndexSelected(index);
    };

    const deleteImage = (index) => () => {
        const newUriImages = [...uriImages];
        newUriImages[index] = '';
        setUriImages(newUriImages);
        setIndexSelected(index);
    };

    const closeCamera = () => {
        setIsVisibleCamera(false);
    };

    const takePicture = (photo) => {
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ path, uri, size, name }) => {
                    const bodyFromData = new FormData();
                    bodyFromData.append('file', {
                        uri,
                        type: 'image/jpg',
                        name
                    });
                    setIsVisibleCamera(false);
                    getImageCDN(bodyFromData)
                        .then((res) => {
                            const remoteURI = API_GET_IMAGE_CDN + res[0];
                            let newUriImages = [...uriImages];
                            newUriImages[indexSelected] = remoteURI;
                            setUriImages(newUriImages);
                            hideBlockUI();
                        })

                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        }
    };

    const onPickerPhoto = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const bodyFromData = new FormData();
                            bodyFromData.append('file', {
                                uri,
                                type: 'image/jpg',
                                name
                            });
                            setIsVisibleCamera(false);
                            getImageCDN(bodyFromData)
                                .then((res) => {
                                    const remoteURI =
                                        API_GET_IMAGE_CDN + res[0];
                                    let newUriImages = [...uriImages];
                                    newUriImages[indexSelected] = remoteURI;
                                    setUriImages(newUriImages);
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                }
            }
        );
    };

    const getFromDataImage = () => {
        const formData = new FormData();
        const mapIndex = [];
        uriImages.forEach((ele, index) => {
            if (!UrlFilesAdjustPrice[index] && !!ele) {
                const uri = ele;
                const type = 'image/jpg';
                const name = `${dateHelper.getTimestamp()}_${index}.jpg`;
                const path = '/AdjustPrice';

                formData.append('file', { uri, type, name });
                formData.append('path', path);
                formData.append('isGenDate', 'false');
                formData.append('isGenName', 'false');
                mapIndex.push(index);
            }
        });
        uploadPicture(formData, mapIndex);
    };

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.content}>
                <View style={styles.inputWrapper}>
                    <View
                        style={[
                            styles.row,
                            {
                                width: '100%',
                                justifyContent: 'space-between'
                            }
                        ]}>
                        <MyText
                            text={'Tổng tiền nộp: '}
                            style={styles.header}
                            children={
                                <MyText
                                    text={'*'}
                                    addSize={-1.5}
                                    style={{ color: COLORS.txtFF0000 }}
                                />
                            }
                        />
                        <MyText
                            text={helper.formatMoney(totalMoney)}
                            style={[
                                styles.header,
                                { color: COLORS.txtD0021B, fontWeight: '600' }
                            ]}
                        />
                    </View>
                    <View style={styles.row}>
                        <MyText
                            text={'Ngày nộp thực tế: '}
                            style={styles.header}
                            children={
                                <MyText
                                    text={'*'}
                                    addSize={-1.5}
                                    style={{ color: COLORS.txtFF0000 }}
                                />
                            }
                        />
                        <DatePicker
                            styleText={{
                                fontSize: 13
                            }}
                            date={date}
                            maxDate={
                                new Date(
                                    new Date(maxDate).setDate(
                                        maxDate.getDate() + 1
                                    )
                                )
                            }
                            minDate={
                                new Date(
                                    new Date(minDate).setDate(
                                        minDate.getDate() - 3
                                    )
                                )
                            }
                            onDateChange={(date) => {
                                onSelectDate(date);
                            }}
                            mode="date"
                        />
                    </View>
                    <View style={styles.rowUser}>
                        <MyText
                            text={'Nhân viên duyệt: '}
                            style={styles.header}
                            children={
                                <MyText
                                    text={'*'}
                                    addSize={-1.5}
                                    style={{ color: COLORS.txtFF0000 }}
                                />
                            }
                        />
                    </View>
                    <View style={styles.vwInput}>
                        <TextInput
                            style={styles.input}
                            value={userId}
                            placeholder={'Nhập MSNV'}
                            onChangeText={(text) => {
                                onChangeTxUser(text);
                            }}
                            blurOnSubmi={true}
                            keyboardType="number-pad"
                            returnKeyType="done"
                            onSubmitEditing={() => {
                                onChangeUser(userId);
                            }}
                        />
                        <View style={styles.txInput}>
                            <MyText text={userName} style={{ fontSize: 14 }} />
                        </View>
                    </View>
                    <MyText
                        text={'Danh sách đính kèm: '}
                        style={styles.header}
                        children={
                            <MyText
                                text={'*'}
                                addSize={-1.5}
                                style={{ color: COLORS.txtFF0000 }}
                            />
                        }
                    />
                    <View style={styles.column}>
                        <ImageAdjust
                            onCamera={openCamera(0)}
                            onDelete={deleteImage(0)}
                            onUpload={getFromDataImage}
                            urlRemote={uriImages[0]}
                            // title={translate('sendbank.download_photo')}
                        />
                        <ImageAdjust
                            onCamera={openCamera(1)}
                            onDelete={deleteImage(1)}
                            onUpload={getFromDataImage}
                            urlRemote={uriImages[1]}
                            // title={translate('sendbank.download_photo')}
                        />
                        <ImageAdjust
                            onCamera={openCamera(2)}
                            onDelete={deleteImage(2)}
                            onUpload={getFromDataImage}
                            urlRemote={uriImages[2]}
                            // title={translate('sendbank.download_photo')}
                        />
                    </View>
                </View>
                <View style={styles.footer}>
                    <Button
                        text={'Quay lại'}
                        onPress={() => navigation.goBack()}
                        styleContainer={styles.button}
                        styleText={styles.buttonText}
                    />
                    <Button
                        text={'Tạo yêu cầu'}
                        onPress={onSearchReceipt}
                        styleContainer={styles.button}
                        styleText={styles.buttonText}
                    />
                </View>
            </View>
            <CaptureCamera
                isVisibleCamera={isVisibleCamera}
                takePicture={takePicture}
                closeCamera={closeCamera}
                selectPicture={onPickerPhoto}
            />
        </SafeAreaView>
    );
};

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
        dataSearchUser: state.sendBankReducer.dataSearchUser
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateRequest);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: COLORS.bgFFFFFF
    },
    content: {
        flex: 1,
        marginHorizontal: 16,
        marginTop: 10
    },
    header: {
        fontWeight: '500',
        marginRight: 5
    },
    title: {
        textAlign: 'center',
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12
    },
    row: {
        minHeight: 42,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: 2
    },
    rowUser: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 2
    },
    column: {
        flexDirection: 'row',
        width: constants.WIDTH
    },
    inputWrapper: {
        flexGrow: 1,
        gap: 5
    },
    inputNumber: {
        flex: 1,
        borderWidth: 1,
        borderRadius: 4,
        borderColor: COLORS.bdCCCCCC,
        height: 40,
        paddingHorizontal: 10,
        backgroundColor: COLORS.bgFFFFFF,
        justifyContent: 'center'
    },
    input: {
        flex: 0.3,
        // width: '40%',
        height: 40,
        borderWidth: 0.5,
        borderRadius: 4,
        borderColor: COLORS.bdCCCCCC,
        paddingHorizontal: 10,
        backgroundColor: COLORS.bgFFFFFF
    },
    button: {
        width: '38%',
        height: 44,
        borderRadius: 10,
        backgroundColor: COLORS.btn2C8BD7
    },
    buttonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: COLORS.txtFFFFFF
    },
    headerRow: {
        paddingHorizontal: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 6
    },
    headerText: {
        textAlign: 'center',
        fontWeight: 'bold'
    },
    tableContainer: {
        marginVertical: 10
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-evenly'
    },
    vwInput: {
        width: constants.WIDTH,
        height: 40,
        // marginHorizontal: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    txInput: {
        flex: 0.7,
        height: 40,
        paddingLeft: 10,
        justifyContent: 'center',
        marginLeft: 10,
        borderRadius: 4,
        borderWidth: 0.5,
        borderColor: COLORS.bdCCCCCC,
        backgroundColor: COLORS.bgFFFFFF
    }
});
