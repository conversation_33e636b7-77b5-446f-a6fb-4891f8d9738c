import {
    apiBase,
    METHOD,
    SUCCE<PERSON>,
    EMPTY,
    ERROR
} from '@config';
import { API_CONST, STORAGE_CONST } from '@constants';
import { helper, storageHelper } from '@common';
import { translate } from '@translate';
import moment from 'moment';
import { mapOcrNewAddress } from '../InstallmentManagerBC/maping/LocationMappingUtils';

const {
    API_SEARCH_INSTALLMENT,
    API_Delete_EPOSTransaction,
    API_GET_BACK_INFORMATION,
    API_GET_INSTALMENT_INFO_F88,
    API_GET_BROADCAST_PAPER_TYPE,
    API_GET_BROADCAST_INSTALMENT_PARTNER,
    API_GET_BROADCAST_SALE_PROGRAM,
    API_GET_BROADCAST_LOAN_PACKAGE,
    API_GET_BROADCAST_INFORMATION,
    API_GET_PAYMENT_AMOUNT_MONTHLY_BROADCAST,
    API_UPDATE_EPOS_BROADCAST,
    API_CHECK_MONTHLY_PAYMENT_BROADCAST,
    API_GET_MAIN_AND_SUBEP_TO_UPDATE,
    API_GET_LIST_APPLICATION_OFFER,
    API_ACCEPT_ALTERNATIVE_OFFERS,
    API_GET_REWARD_INSTALLMENT,
    API_GET_BROADCAST_INFORMATION_NEW,
    API_UPDATE_EPOS_BROADCAST_NEW
} = API_CONST;
const START_SEARCH_INSTALLMENT = 'START_SEARCH_INSTALLMENT';
const STOP_SEARCH_INSTALLMENT = 'STOP_SEARCH_INSTALLMENT';
const START_SEARCH_IDCARDISSUEPLACE = 'START_SEARCH_IDCARDISSUEPLACE';
const STOP_SEARCH_IDCARDISSUEPLACE = 'STOP_SEARCH_IDCARDISSUEPLACE';
const START_SEARCH_IDCARDHOMETOWN = 'START_SEARCH_IDCARDHOMETOWN';
const STOP_SEARCH_IDCARDHOMETOWN = 'STOP_SEARCH_IDCARDHOMETOWN';
const START_SEARCH_REASONCANCEL = 'START_SEARCH_REASONCANCEL';
const STOP_SEARCH_REASONCANCEL = 'STOP_SEARCH_REASONCANCEL';
const UPDATE_DATACACHEEPOSTRANSECTION = 'UPDATE_DATACACHEEPOSTRANSECTION';
const SET_PARAM_FILTER_SEACHDATA = 'SET_PARAM_FILTER_SEACHDATA';
const START_GET_INSTALLMENT_PRINTER = 'START_GET_INSTALLMENT_PRINTER';
const STOP_GET_INSTALLMENT_PRINTER = 'STOP_GET_INSTALLMENT_PRINTER';
const START_GET_INSTALLMENT_LISTREPORT = 'START_GET_INSTALLMENT_LISTREPORT';
const STOP_GET_INSTALLMENT_LISTREPORT = 'STOP_GET_INSTALLMENT_LISTREPORT';
const START_GET_BROADCAST_PAPER_TYPE = 'START_GET_BROADCAST_PAPER_TYPE';
const STOP_GET_BROADCAST_PAPER_TYPE = 'STOP_GET_BROADCAST_PAPER_TYPE';
const START_GET_BROADCAST_INSTALMENT_PARTNER = 'START_GET_BROADCAST_INSTALMENT_PARTNER';
const STOP_GET_BROADCAST_INSTALMENT_PARTNER = 'STOP_GET_BROADCAST_INSTALMENT_PARTNER';
const START_GET_BROADCAST_LOAN_PACKAGE = 'START_GET_BROADCAST_LOAN_PACKAGE';
const STOP_GET_BROADCAST_LOAN_PACKAGE = 'STOP_GET_BROADCAST_LOAN_PACKAGE';
const SET_BROADCAST_PARTNER_LIST = 'SET_BROADCAST_PARTNER_LIST';
const START_GET_REWARD_INSTALLMENT_BC = 'STATE_GET_REWARD_INSTALLMENT_BC';
const STOP_GET_REWARD_INSTALLMENT_BC = 'TOP_GET_REWARD_INSTALLMENT_BC'

export const InstallmentAction = {
    START_SEARCH_INSTALLMENT,
    STOP_SEARCH_INSTALLMENT,
    START_SEARCH_IDCARDISSUEPLACE,
    STOP_SEARCH_IDCARDISSUEPLACE,
    START_SEARCH_IDCARDHOMETOWN,
    STOP_SEARCH_IDCARDHOMETOWN,
    SET_PARAM_FILTER_SEACHDATA,
    START_SEARCH_REASONCANCEL,
    STOP_SEARCH_REASONCANCEL,
    START_GET_INSTALLMENT_PRINTER,
    STOP_GET_INSTALLMENT_PRINTER,
    START_GET_INSTALLMENT_LISTREPORT,
    STOP_GET_INSTALLMENT_LISTREPORT,
    START_GET_BROADCAST_PAPER_TYPE,
    STOP_GET_BROADCAST_PAPER_TYPE,
    START_GET_BROADCAST_INSTALMENT_PARTNER,
    STOP_GET_BROADCAST_INSTALMENT_PARTNER,
    START_GET_BROADCAST_LOAN_PACKAGE,
    STOP_GET_BROADCAST_LOAN_PACKAGE,
    SET_BROADCAST_PARTNER_LIST,
    START_GET_REWARD_INSTALLMENT_BC,
    STOP_GET_REWARD_INSTALLMENT_BC
};

export const getBackProfileInformation = function (IDCard) {
    return new Promise((resolve, reject) => {
        let body = {
            "IDCard": IDCard
        };
        apiBase(API_GET_BACK_INFORMATION, METHOD.POST, body).then((response) => {
            console.log("startBackProfileInformation success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                resolve(object);
            }
        }).catch(error => {
            console.log("startBackProfileInformation error", error);
        });
    });
};

export const startSearchInstallment = () => {
    return {
        type: START_SEARCH_INSTALLMENT
    };
};
export const stopSearchInstallment = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_SEARCH_INSTALLMENT,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

export const startSearchIDCardIssuePlaceList = () => {
    return {
        type: START_SEARCH_IDCARDISSUEPLACE
    };
};
export const stopSearchIDCardIssuePlaceList = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_SEARCH_IDCARDISSUEPLACE,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

export const startSearchIDCardHomeTown = () => {
    return {
        type: START_SEARCH_IDCARDHOMETOWN
    };
};
export const stopSearchIDCardHomeTown = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_SEARCH_IDCARDHOMETOWN,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

export const startSearchReason = () => {
    return {
        type: START_SEARCH_REASONCANCEL
    };
};
export const stopSearchReason = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_SEARCH_REASONCANCEL,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

export const start_get_list_printer = () => {
    return {
        type: START_GET_INSTALLMENT_PRINTER
    };
};

export const stop_get_list_printer = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_GET_INSTALLMENT_PRINTER,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

export const start_get_list_report = () => {
    return {
        type: START_GET_INSTALLMENT_LISTREPORT
    };
};

export const stop_get_list_report = (
    isSuccess,
    lstData = [],
    errorDiscription,
    isEmty
) => {
    return {
        type: STOP_GET_INSTALLMENT_LISTREPORT,
        isSuccess,
        lstData,
        errorDiscription,
        isEmty
    };
};

const start_get_broadcast_paper_type = () => {
    return {
        type: START_GET_BROADCAST_PAPER_TYPE
    };
};
const stop_get_broadcast_paper_type = (
    broadcastPaperType,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BROADCAST_PAPER_TYPE,
        broadcastPaperType,
        isEmpty,
        description,
        isError
    };
};

const start_get_broadcast_instalment_partner = () => {
    return {
        type: START_GET_BROADCAST_INSTALMENT_PARTNER
    };
};
const stop_get_broadcast_instalment_partner = (
    broadcastSaleProgram,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BROADCAST_INSTALMENT_PARTNER,
        broadcastSaleProgram,
        isEmpty,
        description,
        isError
    };
};

const start_get_broadcast_loan_package = () => {
    return {
        type: START_GET_BROADCAST_LOAN_PACKAGE
    };
};

const stop_get_broadcast_loan_package = (
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BROADCAST_LOAN_PACKAGE,
        isEmpty,
        description,
        isError
    };
};

const set_broadcast_partner_list = (partnerList) => {
    return {
        type: SET_BROADCAST_PARTNER_LIST,
        partnerList
    };
};

export const searchInstallment = function (keyword, filter) {
    return function (dispatch, getState) {
        dispatch(startSearchInstallment());
        dispatch(set_broadcast_partner_list([]));
        let body = {
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            loginID: getState().userReducer.userName,
            keyWord: keyword,
            username: filter.isFindYours
                ? getState().userReducer.userName
                : '',
            fromDate: filter.fromDate,
            toDate: filter.toDate,
            storeId: getState().userReducer.storeID,
            isDelete: filter.isPOSIsDeleted ? 1 : 0,
            searchBy: 0,
            // search new
            'loginStoreId': getState().userReducer.storeID,
            "keyword": keyword,
            "userName": filter.isFindYours
                ? getState().userReducer.userName
                : '',
            "isDeleted": filter.isPOSIsDeleted ? 1 : 0,
        };
        apiBase(API_SEARCH_INSTALLMENT, METHOD.POST, body)
            .then((res) => {
                console.log('API_SEARCH_INSTALLMENT');
                console.log(res);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    dispatch(
                        stopSearchInstallment(true, res.object, '', false)
                    );
                    dispatch(set_param_filter(keyword, filter));
                } else {
                    dispatch(
                        stopSearchInstallment(
                            false,
                            res.object,
                            translate('instalmentManager.instalment_record_not_found'),
                            true
                        )
                    );
                }
            })
            .catch((err) => {
                dispatch(
                    stopSearchInstallment(false, [], err.msgError, true)
                );
            });
    };
};

export const deleteEPOSTransaction = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                ...data,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID
            };
            apiBase(API_Delete_EPOSTransaction, METHOD.POST, body)
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    console.log('error deleteEPOSTransaction: ', err);
                    reject(err);
                });
        });
    };
};

export const getIssueplace = function (
    identificationType,
    nationalityID,
    cartID
) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(startSearchIDCardIssuePlaceList());
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                nationalityID: nationalityID,
                identificationType: identificationType,
                cardID: cartID
            };
            console.log(body);
            apiBase(API_CONST.API_GET_SIM_ISSUE_PLACE, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object)
                    ) {
                        resolve(response.object);
                        dispatch(
                            stopSearchIDCardIssuePlaceList(
                                true,
                                response.object,
                                '',
                                false
                            )
                        );
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_place')
                        });
                        dispatch(
                            stopSearchIDCardIssuePlaceList(
                                false,
                                response.object,
                                translate('instalmentManager.no_place'),
                                true
                            )
                        );
                    }
                    console.log('response getIssueplace: ', response);
                })
                .catch((error) => {
                    reject(error);
                    dispatch(
                        stopSearchIDCardIssuePlaceList(
                            false,
                            [],
                            err.msgError,
                            true
                        )
                    );
                    console.log('error getIssueplace: ', error);
                });
        });
    };
};

export const getEPTransaction = function (EPTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: EPTransactionID
            };
            console.log(body);
            apiBase(API_CONST.API_GET_EPOSTRANSECTION, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isObject(response.object)
                    ) {
                        resolve(response.object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_instalment_information')
                        });
                    }
                    console.log('response getEPTransaction: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getEPTransaction: ', error);
                });
        });
    };
};

export const getEPTransactionNew = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                ePTransactionId: data.EPTransactionID,
                times: data.times
            };
            console.log(body);
            apiBase(API_CONST.API_GET_EPOSTRANSECTION_NEW, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isObject(response.object)
                    ) {
                        resolve(response.object);
                        console.log("getEPTransactionNew", response.object)
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_instalment_information')
                        });
                    }
                    console.log('response getEPTransaction: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getEPTransaction: ', error);
                });
        });
    };
};

export const ocrCCCD = function (bodyFromData) {
    console.log(JSON.stringify(bodyFromData));
    return new Promise((resolve, reject) => {
        apiBase(
            API_CONST.API_READ_INFO_BY_IMAGE,
            METHOD.POST,
            bodyFromData,
            { "isCustomToken": true, "isUpload": true, setTimeOut: 20000 }
        )
            .then(async (response) => {
                if (
                    helper.hasProperty(response, 'object') &&
                    !helper.IsEmptyObject(response.object)
                ) {
                    const { object } = response;
                    const momentBirthday = moment(object.date_of_birth, "YYYY-MM-DD");
                    object.date_of_birth = momentBirthday.isValid() ? `${momentBirthday.format('YYYY-MM-DD')}T00:00:00` : "";
                    const momentExpiriedDate = moment(object.expiration_date, "YYYY-MM-DD");
                    object.expiration_date = momentExpiriedDate.isValid() ? `${momentExpiriedDate.format('YYYY-MM-DD')}T00:00:00` : "";
                    const newObject = await mapOcrNewAddress(object);
                    resolve(newObject);
                } else {
                    reject(response);
                }
                console.log('ocrCCCD success', response);
            })
            .catch((error) => {
                reject(error);
                console.log('ocrCCCD error', error);
            });
    });
};

export const ocrWarranty = function (bodyFromData) {
    console.log(JSON.stringify(bodyFromData));
    return new Promise((resolve, reject) => {
        apiBase(
            API_CONST.API_GET_EXTRACT_IPHONE_INFO,
            METHOD.POST,
            bodyFromData,
            { "isCustomToken": true, "isUpload": true, setTimeOut: 20000 }
        )
            .then(async (response) => {
                if (
                    helper.hasProperty(response, 'object') &&
                    !helper.IsEmptyObject(response.object)
                ) {
                    const { object } = response;
                    resolve(object);
                    console.log('ocrWarranty success', response);
                } else {
                    reject(response);
                }

            })
            .catch((error) => {
                reject(error);
                console.log('ocrWarranty error', error);
            });
    });
};

export const set_param_filter = (keyword, paramFilter) => {
    return {
        type: SET_PARAM_FILTER_SEACHDATA,
        keyword,
        paramFilter
    };
};

export const getPaperTypeInforByEP = function (objEPOSTransactionBO) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: objEPOSTransactionBO
            };
            console.log(body);
            apiBase(API_CONST.API_GET_GETPAPERTYPEINFOBYEP, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object)
                    ) {
                        resolve(response.object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.no_record_type')
                        });
                    }
                    console.log('response GetPaperTypeInforByEP: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error GetPaperTypeInforByEP: ', error);
                });
        });
    };
};

export const getPaymentMonthly = function (objEPOSTransactionBO) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: objEPOSTransactionBO
            };
            console.log(body);
            apiBase(API_CONST.API_GET_PAYMENTMONTHLY, METHOD.POST, body)
                .then((response) => {
                    console.log('getPaymentMonthly success', response);
                    const { object } = response;
                    if (
                        !response.error &&
                        helper.hasProperty(object, 'PAYMENTAMOUNTMONTHLY')
                    ) {
                        const { PAYMENTAMOUNTMONTHLY } = object;
                        resolve(PAYMENTAMOUNTMONTHLY);
                    } else {
                        reject(translate('instalmentManager.no_payment_per_month_information'));
                    }
                })
                .catch((error) => {
                    console.log('getPaymentMonthly error', error);
                    reject(error.msgError);
                });
        });
    };
};

export const getInfoDriverGLXByImage = function (bodyFromData) {
    console.log(JSON.stringify(bodyFromData));
    return new Promise((resolve, reject) => {
        apiBase(
            API_CONST.API_READ_INFO_BY_IMAGE_DRIVERGLX,
            METHOD.POST,
            bodyFromData,
            { "isCustomToken": true, "isUpload": true }
        )
            .then((response) => {
                if (
                    helper.hasProperty(response, 'object') &&
                    !helper.IsEmptyObject(response.object)
                ) {
                    resolve(response.object);
                } else {
                    reject(response);
                }
                console.log('getInfoDriverGLXByImage', response);
            })
            .catch((error) => {
                reject(error);
                console.log('getInfoDriverGLXByImage', error);
            });
    });
};

export const updateEPosTransaction = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(API_CONST.API_GET_UPDATEEPOSTRANSECTION, METHOD.POST, {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: params.EPOSTransactionBO,
                GroupPaperTypeInforBOList: params.GroupPaperTypeInforBOList
            })
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('updateEPosTransaction', response.object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_cancel_reason')
                        });
                    }
                    console.log('response updateEPosTransaction: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateEPosTransaction: ', error);
                });
        });
    };
};

export const updateEPosTransactionNew = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(API_CONST.API_GET_UPDATEEPOSTRANSECTION_NEW, METHOD.POST, {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                ePosTransactionBo: data.EPOSTransactionBO,
                groupPaperTypeInforBoList: data.GroupPaperTypeInforBOList,
                times: data.times
            })
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('updateEPosTransactionNew success', response.object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_cancel_reason')
                        });
                    }
                    console.log('response updateEPosTransaction: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateEPosTransaction: ', error);
                });
        });
    };
};

export const updateBroadcastTransaction = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(API_UPDATE_EPOS_BROADCAST, METHOD.POST, {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: data.EPOSTransactionBO,
                ListSaleProgramBO: data.listSaleProgramBO,
                ListGroupPaperTypeInforBO: data.listGroupPaperTypeInforBO,
                RootPath: "1111"
            })
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('updateBroadcastTransaction', object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_cancel_reason')
                        });
                    }
                    console.log('response updateBroadcastTransaction: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateBroadcastTransaction: ', error);
                });
        });
    };
};

export const updateBroadcastTransactionNew = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(API_UPDATE_EPOS_BROADCAST_NEW, METHOD.POST, {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: data.EPOSTransactionBO,
                ListSaleProgramBO: data.listSaleProgramBO,
                ListGroupPaperTypeInforBO: data.listGroupPaperTypeInforBO,
                RootPath: "1111",
                times: data.times
            })
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('updateBroadcastTransactionNew', object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_cancel_reason')
                        });
                    }
                    console.log('response updateBroadcastTransactionNew: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateBroadcastTransactionNew: ', error);
                });
        });
    };
};

export const getDataEPOSCancelReason = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            // dispatch(startSearchReason());
            let body = {
                ...params,
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID
            };
            console.log(body);
            apiBase(API_CONST.API_INSATLLMENT_REASONCANCEL, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        object.length > 0
                    ) {
                        resolve(response.object);
                        // dispatch(
                        //   stopSearchReason(
                        //     true,
                        //     response.object,
                        //     "",
                        //     false
                        //   )
                        // );
                    } else {
                        resolve(response.object);
                        // dispatch(
                        //   stopSearchReason(
                        //     false,
                        //     response.object,
                        //     translate('instalmentManager.no_hometown_information'),
                        //     true
                        //   )
                        // );
                    }
                    console.log('response getDataEPOSCancelReason: ', response);
                })
                .catch((error) => {
                    reject(error);
                    // dispatch(stopSearchReason(false, [], err.msgError, true));
                    console.log('error getDataEPOSCancelReason: ', error);
                });
        });
    };
};

export const getDataIDCardHomeTown = function () {
    return function (dispatch, getState) {
        dispatch(startSearchIDCardHomeTown());
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID
        };
        console.log(body);
        apiBase(API_CONST.API_GET_IDCARDHOMETOWN, METHOD.POST, body)
            .then((response) => {
                const { object } = response;
                if (
                    helper.hasProperty(response, 'object') &&
                    helper.isArray(response.object) &&
                    object.length > 0
                ) {
                    dispatch(
                        stopSearchIDCardHomeTown(
                            true,
                            response.object,
                            '',
                            false
                        )
                    );
                } else {
                    dispatch(
                        stopSearchIDCardHomeTown(
                            false,
                            response.object,
                            translate('instalmentManager.no_cancel_reason'),
                            true
                        )
                    );
                }
                console.log('response getDataIDCardHomeTown: ', response);
            })
            .catch((error) => {
                dispatch(
                    stopSearchIDCardHomeTown(false, [], err.msgError, true)
                );
                console.log('error getDataIDCardHomeTown: ', error);
            });
    };
};

export const getStatusEPOSTransaction = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID
            };
            console.log(body);
            apiBase(API_CONST.API_INSTALLMENT_GETSTATUS, METHOD.POST, body)
                .then((response) => {
                    console.log('getStatusEPOSTransaction success', response);
                    const { object } = response;
                    if (!response.error) {
                        resolve(object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.no_instalment_information')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getStatusEPOSTransaction: ', error);
                });
        });
    };
};

export const submitApplication = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID
            };
            console.log(body);
            apiBase(
                API_CONST.API_INSTALLMENT_SUBMITAPPLICATION,
                METHOD.POST,
                body
            )
                .then((response) => {
                    console.log('submitApplication success', response);
                    const { object } = response;
                    if (!response.error) {
                        resolve(object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.no_record_code_sent_to_partner')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error submitApplication: ', error);
                });
        });
    };
};

export const getPrepareContractInfo = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID
            };
            console.log(body);
            apiBase(
                API_CONST.API_INSTALLMENT_GETPREPARECONTRACT,
                METHOD.POST,
                body
            )
                .then((response) => {
                    console.log('getPrepareContractInfo success', response);
                    const { object } = response;
                    if (!response.error) {
                        resolve(object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.error_get_signature')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getPrepareContractInfo: ', error);
                });
        });
    };
};

export const resendOTP = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID
            };
            console.log(body);
            apiBase(API_CONST.API_INSTALLMENT_RESENDOTP, METHOD.POST, body)
                .then((response) => {
                    console.log('resendOTP success', response);
                    const { object } = response;
                    if (!response.error) {
                        resolve(object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.error_resend_otp')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error resendOTP: ', error);
                });
        });
    };
};

export const verifyOTP = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID,
                OTPCode: params.OTPCode
            };
            console.log(body);
            apiBase(API_CONST.API_INSTALLMENT_VERIFYOTP, METHOD.POST, body)
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('VerifyOTP success ', response.object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.cannot_verify_otp')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error VerifyOTP: ', error);
                });
        });
    };
};

export const getAttachmentTypeByID = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            // dispatch(startSearchReason());
            let body = {
                ...params,
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID
            };
            console.log(body);
            apiBase(
                API_CONST.API_INSATLLMENT_GETATTACHMENTTYPE,
                METHOD.POST,
                body
            )
                .then((response) => {
                    const { object } = response;
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        object.length > 0
                    ) {
                        resolve(response.object);
                    } else {
                        reject(response);
                    }
                    console.log('response getAttachmentTypeByID: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getAttachmentTypeByID: ', error);
                });
        });
    };
};

export const getUser = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            // dispatch(startSearchReason());
            let body = {
                ...params
            };
            console.log(body);
            apiBase(API_CONST.API_SEARCHUSER, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        object.length > 0
                    ) {
                        resolve(response.object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.no_agent_information_found')
                        });
                    }
                    console.log('response getUser: ', response);
                })
                .catch((error) => {
                    //reject(error);
                    reject({
                        msgError:
                            translate('instalmentManager.error_get_agent_information')
                    });
                    console.log('error getUser: ', error);
                });
        });
    };
};

export const updateAddAttachmentInfor = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(
                API_CONST.API_INSATLLMENT_UPDATEATTACHMENTTYPE,
                METHOD.POST,
                {
                    ...params,
                    loginStoreId: getState().userReducer.storeID,
                    languageID: getState().userReducer.languageID,
                    moduleID: getState().userReducer.moduleID
                }
            )
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log(
                            'updateAddAttachmentInfor',
                            response.object
                        );
                    } else {
                        reject({
                            msgError: translate('instalmentManager.error_add_record_partner')
                        });
                    }
                    console.log(
                        'response updateAddAttachmentInfor: ',
                        response
                    );
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateAddAttachmentInfor: ', error);
                });
        });
    };
};

export const sendAttachFileToPartner = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: params.EPTransactionID
            };
            console.log(body);
            apiBase(
                API_CONST.API_INSATLLMENT_SENDACTTACHFILE,
                METHOD.POST,
                body
            )
                .then((response) => {
                    console.log('sendAttachFileToPartner success', response);
                    const { object } = response;
                    if (!response.error) {
                        resolve(object);
                    } else {
                        reject({
                            msgError:
                                translate('instalmentManager.error_send_record')
                        });
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('error sendAttachFileToPartner: ', error);
                });
        });
    };
};

export const checkurlcontract = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            apiBase(API_CONST.API_INSATLLMENT_PRINTCONTRACT, METHOD.POST, {
                ...params,
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID
            })
                .then((response) => {
                    if (helper.hasProperty(response, 'object')) {
                        const { object } = response;
                        resolve(object);
                        console.log('checkurlcontract', response.object);
                    } else {
                        reject({
                            msgError: translate('instalmentManager.error_print_contract')
                        });
                    }
                    console.log('response checkurlcontract: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error updateAddAttachmentInfor: ', error);
                });
        });
    };
};

export const getPrinter = function () {
    return function (dispatch, getState) {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            moduleID: getState().userReducer.moduleID,
            languageID: getState().userReducer.languageID,
            orderTypeID: 0
        };
        dispatch(start_get_list_printer());
        apiBase(API_CONST.API_GET_INSTALLMENT_PRINTER, METHOD.POST, body)
            .then((response) => {
                if (
                    helper.hasProperty(response, 'object') &&
                    helper.isArray(response.object) &&
                    response.object.length > 0
                ) {
                    dispatch(stop_get_list_printer(SUCCESS, response.object));
                } else {
                    dispatch(
                        stop_get_list_printer(
                            !SUCCESS,
                            [],
                            translate('instalmentManager.no_printer_information'),
                            EMPTY
                        )
                    );
                }
                console.log('List Printer', response);
            })
            .catch((error) => {
                console.log('Err printer', error);
                dispatch(stop_get_list_printer(!SUCCESS, [], error.msgError));
            });
    };
};

export const getListReprint = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_list_report());
            let body = {
                ...params,
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID
            };
            apiBase(
                API_CONST.API_GET_INSTALLMENT_LIST_REPORT,
                METHOD.POST,
                body
            )
                .then((res) => {
                    if (
                        helper.hasProperty(res, 'object') &&
                        helper.isArray(res.object) &&
                        res.object.length > 0
                    ) {
                        resolve(res.object);
                        dispatch(
                            stop_get_list_report(false, res.object, '', false)
                        );
                    } else {
                        resolve(res.object);
                        dispatch(
                            stop_get_list_report(
                                false,
                                res.object,
                                translate('instalmentManager.no_print_format_found'),
                                true
                            )
                        );
                    }
                })
                .catch((err) => {
                    dispatch(
                        stop_get_list_report(true, [], err.msgError, false)
                    );
                });
        });
    };
};

export const DataCacheInstallmentAction = {
    UPDATE_DATACACHEEPOSTRANSECTION
};

export const printPDFFromFileUrl = function (formBody) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            helper.LoggerDebug({ bodyPrint: formBody });
            fetch(API_CONST.API_REQUEST_PRINT_PDF_FROMFILEURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formBody
            })
                .then((response) => response.text())
                .then((textXML) => {
                    helper.LoggerDebug({ responsePrint: textXML });
                    console.log('printPDFFromFileUrl', textXML);
                    if (textXML.includes('succes')) {
                        resolve(textXML);
                    } else {
                        reject(translate('instalmentManager.error_print'));
                    }
                })
                .catch((err) => {
                    console.log('printPDFFromFileUrl', err);
                    reject(translate('instalmentManager.cannot_connect_to_printer'));
                });
            setTimeout(() => {
                reject(translate('instalmentManager.cannot_connect_to_printer'));
            }, 30000);
        });
    };
};

export const getInstalmentInfoF88 = (epTransactionId, IsQueryStatus) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "EPTransactionID": epTransactionId,
                "IsQueryStatus": IsQueryStatus
            };
            apiBase(API_GET_INSTALMENT_INFO_F88, METHOD.POST, body).then((response) => {
                console.log("getInstalmentInfoF88 success: ", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('f88.fail_conect'));
                }
            }).catch(error => {
                console.log("getInstalmentInfoF88 error: ", error);
                reject(error.msgError);
            });
        });
    };
};

export const getBroadcastPaperType = () => {
    return function (dispatch, getState) {
        const currentBroadcastPaperType = getState().InstallmentReducer.broadcastPaperType;
        if (helper.IsNonEmptyArray(currentBroadcastPaperType)) {
            dispatch(stop_get_broadcast_paper_type(currentBroadcastPaperType));
        } else {
            dispatch(start_get_broadcast_paper_type());
            let body = {
                "loginuser": getState().userReducer.userName,
                "loginstore": getState().userReducer.storeID,
                "moduleID": getState().userReducer.moduleID,
                "languageID": getState().userReducer.languageID,
                "data_object": {
                    "data": null
                }
            };
            apiBase(API_GET_BROADCAST_PAPER_TYPE, METHOD.POST, body)
                .then((response) => {
                    console.log("getBroadcastPaperType success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        dispatch(stop_get_broadcast_paper_type(object));
                    } else {
                        dispatch(stop_get_broadcast_paper_type([], true, 'Không lấy được danh sách các loại giấy tờ', false));
                    }
                })
                .catch((err) => {
                    dispatch(stop_get_broadcast_paper_type([], false, err.msgError, true));
                    console.log("getBroadcastPaperType error", err);
                });
        }
    };
};

export const getBroadcastInstalmentPartner = (data) => {
    return function (dispatch, getState) {
        dispatch(start_get_broadcast_instalment_partner());
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            EPTransactionID: data.epTransactionID,
            ListPaperType: data.listPaperType,
            PartnerInstallmentID: data.partnerInstallmentID,
            SaleProgramID: data.saleProgramID
        };
        apiBase(API_GET_BROADCAST_INSTALMENT_PARTNER, METHOD.POST, body)
            .then((response) => {
                console.log("getBroadcastInstalmentPartner success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_broadcast_instalment_partner(object));
                } else {
                    dispatch(stop_get_broadcast_instalment_partner([], true, 'Không có chương trình trả góp cho các loại giấy tờ đã chọn', false));
                }
            })
            .catch((err) => {
                dispatch(stop_get_broadcast_instalment_partner([], false, err.msgError, true));
                console.log("getBroadcastInstalmentPartner error", err);
            });
    };
};

export const getBroadcastSaleProgram = (data, partnerIndex) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPTransactionID: data.EPTransactionID,
                ListPaperType: data.ListPaperType,
                PartnerInstallmentID: data.PartnerInstallmentID //khác (HS phụ)
            };
            apiBase(API_GET_BROADCAST_SALE_PROGRAM, METHOD.POST, body)
                .then((response) => {
                    console.log("getBroadcastSaleProgram success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        let newBroadcastSaleProgram = getState().InstallmentReducer.broadcastSaleProgram;
                        newBroadcastSaleProgram[partnerIndex].cus_ListSaleProgramBO = object;
                        dispatch(stop_get_broadcast_instalment_partner(newBroadcastSaleProgram));
                        resolve(newBroadcastSaleProgram);
                    } else {
                        reject('Không tìm thấy chương trình trả góp của đối tác');
                    }
                })
                .catch((err) => {
                    reject(err.msgError);
                    console.log("getBroadcastSaleProgram error", err);
                });
        });
    };
};

export const updateBroadcastSaleProgram = (data) => {
    return function (dispatch, getState) {
        dispatch(stop_get_broadcast_instalment_partner(data));
    };
};

export const getBroadcastLoanPackage = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_broadcast_loan_package());
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: data.EPOSTransactionBO,
                SaleProgramBO: data.SaleProgramBO
            };
            apiBase(API_GET_BROADCAST_LOAN_PACKAGE, METHOD.POST, body)
                .then((response) => {
                    console.log("getBroadcastLoanPackage success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_broadcast_loan_package());
                        resolve(object);
                    } else {
                        dispatch(stop_get_broadcast_loan_package(true, 'Không có thông tin kì hạn vay của chương trình trả góp đã chọn', false));
                    }
                })
                .catch((err) => {
                    dispatch(stop_get_broadcast_loan_package(false, err.msgError, true));
                    console.log("getBroadcastLoanPackage error", err);
                });
        });
    };
};

export const getBroadcastInformation = function (EPOSTransactionBO, listPaperTypeID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO,
                ListPaperTypeID: listPaperTypeID
            };
            console.log(body);
            apiBase(API_GET_BROADCAST_INFORMATION, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        resolve(response.object);
                    } else {
                        reject({ msgError: translate('instalmentManager.no_instalment_information') });
                    }
                    console.log('getBroadcastInformation success: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('getBroadcastInformation error: ', error);
                });
        });
    };
};

export const getBroadcastInformationNew = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                EPOSTransactionBO: data.EPOSTransactionBO,
                ListPaperTypeID: data.ListPaperTypeID,
                times: data.times
            };
            console.log(body);
            apiBase(API_GET_BROADCAST_INFORMATION_NEW, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        resolve(response.object);
                    } else {
                        reject({ msgError: translate('instalmentManager.no_instalment_information') });
                    }
                    console.log('getBroadcastInformationNew success: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('getBroadcastInformationNew error: ', error);
                });
        });
    };
};

export const getBroadcastInEnterASecondTime = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "EPOSTransactionBO": {
                    epTransactionId: data.epTransactionId,
                    eposTransactionId: data.eposTransactionId,
                },
                times: data.times
            };
            console.log(body);
            apiBase(API_GET_BROADCAST_INFORMATION_NEW, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        resolve(response.object);
                    } else {
                        reject({ msgError: translate('instalmentManager.no_instalment_information') });
                    }
                    console.log('getBroadcastInformationNew success: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('getBroadcastInformationNew error: ', error);
                });
        });
    };
};

export const updateDataCacheEposTransection = (lstData = []) => {
    return {
        type: UPDATE_DATACACHEEPOSTRANSECTION,
        lstData
    };
};

export const DeleteAndCreatedDataCacheEPByStep = function (data) {
    return function (dispatch, getState) {
        dispatch(updateDataCacheEposTransection(data));
    };
};

export const getPaymentAmountMonthlyBroadcast = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "EPOSTransactionBO": data.EPOSTransactionBO,
                "SaleProgramBO": data.SaleProgramBO,
                "ListPaperTypeID": data.ListPaperTypeID
            };
            apiBase(API_GET_PAYMENT_AMOUNT_MONTHLY_BROADCAST, METHOD.POST, body).then((response) => {
                console.log("getPaymentAmountMonthlyBroadcast success", response);
                const { object } = response;
                if (helper.hasProperty(object, "PAYMENTAMOUNTMONTHLY")) {
                    const { PAYMENTAMOUNTMONTHLY } = object;
                    resolve(PAYMENTAMOUNTMONTHLY);
                }
                else {
                    reject(translate('shoppingCart.no_information_payment_month'));
                }
            }).catch(error => {
                console.log("getPaymentAmountMonthlyBroadcast error", error);
                reject(error.msgError);
            });
        });
    };
};

export const checkMonthlyPaymentBroadcast = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "EPOSTransactionBO": data.EPOSTransactionBO,
                "SaleProgramBO": data.SaleProgramBO,
                "ListPaperTypeID": data.ListPaperTypeID
            };
            apiBase(API_CHECK_MONTHLY_PAYMENT_BROADCAST, METHOD.POST, body).then((response) => {
                console.log("checkMonthlyPaymentBroadcast success", response);
                const { object } = response;
                if (object) {
                    resolve(true);
                } else {
                    reject("Số tiền trả hàng tháng không hợp lệ");
                }
            }).catch(error => {
                console.log("checkMonthlyPaymentBroadcast error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getMainAndSubepToUpdate = function (EPOSTransactionID) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "EPOSTransactionID": EPOSTransactionID
            };
            apiBase(API_GET_MAIN_AND_SUBEP_TO_UPDATE, METHOD.POST, body).then((response) => {
                console.log("getMainAndSubepToUpdate success", response);
                const { object } = response;
                if (object) {
                    resolve(true);
                } else {
                    reject("Hồ sơ không thõa điều kiện để cập nhật chính thức.");
                }
            }).catch(error => {
                console.log("getMainAndSubepToUpdate error", error);
                reject(error.msgError);
            })
        });
    }
}

export const setBroadcastPartnerList = function (partnerList) {
    return function (dispatch, getState) {
        dispatch(set_broadcast_partner_list(partnerList));
    };
};

export const getListApplicationOffer = function (epTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "EPTransactionID": epTransactionID
            };
            apiBase(API_GET_LIST_APPLICATION_OFFER, METHOD.POST, body)
                .then((res) => {
                    resolve(res.object);
                    console.log('updateBroadcastTransaction sucess', res.object);
                })
                .catch((err) => {
                    console.log('error getListApplicationOffer: ', err);
                    reject(err);
                });
        });
    };
};

export const getAcceptAlternativeOffers = function (params) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "EPOSTransactionOfferInfoBO": params.data
            };
            apiBase(API_ACCEPT_ALTERNATIVE_OFFERS, METHOD.POST, body)
                .then((res) => {
                    resolve(res.object);
                    console.log('getAcceptAlternativeOffers sucess', res.object);
                })
                .catch((err) => {
                    console.log('error getAcceptAlternativeOffers: ', err);
                    reject(err);
                });
        });
    };
};

export const getRewardInstallment = (PartnerInstallmentID) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_reward_installment_bc());
            let body = {
                "epPartnerInstallmentID": PartnerInstallmentID,
                "soOrderTypeID": 6,
                "epCreatedStoreID": getState().userReducer.storeID,
            }
            apiBase(API_GET_REWARD_INSTALLMENT, METHOD.POST, body).then((response) => {
                console.log("getRewardInstallment success: ", response);
                const { responseData } = response;
                if (!helper.IsEmptyObject(responseData)) {
                    resolve(responseData)
                    dispatch(stop_get_reward_installment_bc(responseData))
                }
                else {
                    dispatch(stop_get_reward_installment_bc({}, EMPTY, "Không tìm thấy điểm thưởng áp dụng cho chường trình trả góp"))
                }
            }).catch(error => {
                console.log("getRewardInstallment error: ", error);
                dispatch(stop_get_reward_installment_bc({}, false, error.msgError, ERROR))
            })
        })
    }
}

const start_get_reward_installment_bc = () => {
    return ({
        type: START_GET_REWARD_INSTALLMENT_BC
    })
}

export const stop_get_reward_installment_bc = (
    dataRewardInstallmentBC,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_REWARD_INSTALLMENT_BC,
        dataRewardInstallmentBC,
        isEmpty,
        description,
        isError,
    });
}