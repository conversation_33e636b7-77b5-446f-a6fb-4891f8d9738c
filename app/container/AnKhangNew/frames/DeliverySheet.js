/* eslint-disable react/jsx-fragments */
import React, {
    forwardRef,
    useImperativeHandle,
    useRef,
    useState
} from 'react';
import { Alert, Keyboard, View } from 'react-native';

import { MyText, BaseLoading } from '@components';
import { constants } from '@constants';

import { helper } from '@common';
import { keys, translate } from '@translate';
import { useDispatch, useSelector } from 'react-redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Sheet } from '../components';
import useTheme from '../useTheme';
import ListHomeDelivery from '../containers/ListHomeDelivery';
import { useDelivery, useDeliveryDispatch } from '../context/DeliveryProvider';
import {
    checkFeeShipping,
    resetDeliveryInfo,
    setDeliveryAddress,
    setDeliveryContact
} from '../action';

const DeliverySheet = forwardRef(
    (
        {
            products,
            shopCart,
            onGoNext,
            getCurrentIndex,
            onCloseSheet,
            isDisableCustomerPhone,
            ...props
        },
        ref
    ) => {
        const dispatch = useDispatch();
        const [dataShippingRoute, setDataShippingRoute] = useState([]);
        const inputAddressFocus = useRef(false)
        const deliveryDispatch = useDeliveryDispatch();
        const {
            listDeliveryInfo,
            deliveryAddress: addressId,
            deliveryContact: contactInfo
        } = useDelivery();
        const { provinceID } = useSelector((state) => state.userReducer);
        const { customerInfo } = useSelector((state) => state._pharmacyReducer);

        const {
            customerInfo: { ContactName: CustomerContactName }
        } = useSelector((state) => state._pharmacyReducer);
        const sheetRef = useRef(null);
        const handleGoNext = async () => {

            if (inputAddressFocus.current) {
                Keyboard.dismiss()
                inputAddressFocus.current = false
                return
            }
            if (!helper.IsEmptyObject(listDeliveryInfo)) {
                const newListDelivery = Object.values(listDeliveryInfo).map(
                    (delivery) => {
                        return {
                            ...delivery,
                            ContactPhone: contactInfo.contactPhone,
                            ContactName: contactInfo.contactName,
                            ContactGender: contactInfo.gender,
                            DeliveryAddress: contactInfo.contactAddress,
                            DeliveryDistrictID: addressId.districtID,
                            DeliveryProvinceID: addressId.provinceID,
                            DeliveryWardID: addressId.wardID
                        };
                    }
                );
                const { contactPhone, contactName, gender, contactAddress } =
                    contactInfo;
                const isValidate = checkValidateInfo({
                    contactAddress,
                    contactName,
                    contactPhone,
                    gender,
                    CustomerContactName,
                    customerInfo
                });
                if (isValidate) {
                    const idx = newListDelivery.findIndex((delivery) =>
                        helper.IsNonEmptyString(delivery.DeliveryTime)
                    );
                    if (
                        idx !== -1 &&
                        helper.IsNonEmptyArray(dataShippingRoute)
                    ) {
                        const newDataShippingRoute = new Map(
                            dataShippingRoute.map((a) => [
                                `${a.ProductID}_${a.SaleOrderDetailID}`,
                                a.InstockQuantity
                            ])
                        );
                        const newSaleOrderDetails =
                            shopCart.SaleOrderDetails.map((p) => ({
                                ...p,
                                cus_InstockQuantity: newDataShippingRoute.get(
                                    `${p.ProductID}_${p.SaleOrderDetailID}`
                                )
                            }));
                        shopCart.SaleOrderDetails = newSaleOrderDetails;
                        if (shopCart?.CustomerMembershipBO?.IsCheckCustomerMenbership && helper.IsNonEmptyString(contactPhone)) {
                            const customerMembership = await dispatch(checkFeeShipping(contactPhone))
                            if (!helper.IsEmptyObject(customerMembership)) {
                                shopCart.CustomerMembershipBO = customerMembership
                            }
                        }
                        onGoNext({
                            deliveryInfos: newListDelivery,
                            cartRequest: shopCart
                        });
                    } else {
                        Alert.alert(
                            '',
                            translate('detail.please_select_delivery_time')
                        );
                    }
                }
            } else {
                Alert.alert(
                    '',
                    translate('detail.please_select_delivery_time')
                );
            }
        };

        useImperativeHandle(ref, () => ({
            snapToIndex: (index) => {
                sheetRef.current.snapToIndex(index);
            },
            close: () => {
                sheetRef.current.close();
            }
        }));
        return (
            <Sheet
                scrollWrap={false}
                onGoNext={handleGoNext}
                Header={<Header title="Giao Hàng" />}
                onChange={(index) => {
                    // onClose method => unexpected call multiple times
                    if (index === -1) {
                        onCloseSheet();
                        deliveryDispatch(resetDeliveryInfo());

                        if (products.length === 0) {
                            deliveryDispatch(
                                setDeliveryAddress({
                                    wardID: 0,
                                    districtID: 0,
                                    provinceID
                                })
                            );
                            deliveryDispatch(
                                setDeliveryContact({
                                    gender: null,
                                    contactPhone: '',
                                    contactName: '',
                                    contactAddress: ''
                                })
                            );
                        }
                    }

                    getCurrentIndex(index);
                }}
                ref={sheetRef}
                {...props}>
                <BaseLoading
                    content={
                        <KeyboardAwareScrollView
                            style={{
                                marginVertical: 10,
                                flex: 1
                            }}
                            scrollEnabled
                            extraScrollHeight={84}
                            keyboardShouldPersistTaps="handled"
                            enableAutoAutomaticScroll={false}
                            enableOnAndroid>
                            <ListHomeDelivery
                                shopCart={shopCart}
                                handleDataShippingRoute={(data) => {
                                    setDataShippingRoute(data);
                                }}
                                isDisableCustomerPhone={isDisableCustomerPhone}
                                inputAddressFocus={inputAddressFocus}
                            />
                        </KeyboardAwareScrollView>
                    }
                />
            </Sheet>
        );
    }
);

export const Header = ({ title = 'TIÊU ĐỀ' }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <View
            style={{
                borderBottomWidth: 1,
                borderBottomColor: COLOR.darkGray,
                backgroundColor: COLOR.white,
                width: constants.width
            }}>
            <MyText
                selectable={false}
                style={{
                    fontSize: 15,
                    textAlign: 'center',
                    paddingBottom: 8,
                    fontWeight: '500',
                    marginHorizontal: 8
                }}
                numberOfLines={2}
                text={title}
            />
        </View>
    );
};

export default DeliverySheet;

export const checkValidateInfo = ({
    contactPhone,
    contactName,
    contactAddress,
    gender,
    CustomerContactName,
    customerInfo
}) => {
    const { CustomerName, CustomerAddress, TaxID, IsCompany } = customerInfo;
    const regExpTax10 = new RegExp(/^\d{10}$/);
    const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
    const isValidateTax10 = regExpTax10.test(TaxID);
    const isValidateTax14 = regExpTax14.test(TaxID);
    const isValidateTax = isValidateTax10 || isValidateTax14;
    if (IsCompany) {
        if (!helper.IsNonEmptyString(TaxID)) {
            Alert.alert('', translate(keys.shoppingCart.please_enter_tax_code));
            return false;
        }
        if (!isValidateTax) {
            Alert.alert('', translate(keys.shoppingCart.validation_tax));
            return false;
        }
        if (!helper.IsNonEmptyString(CustomerName)) {
            Alert.alert(
                '',
                translate(keys.shoppingCart.validation_company_name)
            );
            return false;
        }
        if (!helper.IsNonEmptyString(CustomerAddress)) {
            Alert.alert(
                '',
                translate(keys.shoppingCart.validation_company_address)
            );
            return false;
        }
    }
    // const regExpPhone = new RegExp(/^[0]\d{9}$/);
    const isValidatePhone = helper.isValidatePhone(contactPhone);
    if (!helper.IsNonEmptyString(contactPhone)) {
        Alert.alert('', translate('detail.please_enter_contact_phone_number'));
        return false;
    }
    if (!isValidatePhone) {
        Alert.alert(
            '',
            translate('detail.please_enter_10_digits_phone_number')
        );
        return false;
    }
    if (!helper.isValidatePhonePrefix(contactPhone)) {
        Alert.alert(
            '',
            translate('detail.please_enter_correct_phone_number_header')
        );
        return false;
    }
    if (!helper.IsNonEmptyString(contactName)) {
        Alert.alert('', translate('detail.please_enter_contact_name'));
        return false;
    }
    if (!helper.IsNonEmptyString(contactAddress)) {
        Alert.alert('', translate('detail.please_enter_delivery_address'));
        return false;
    }

    if (gender == null) {
        Alert.alert('', translate('shoppingCart.validation_gender'));
        return false;
    }
    if (!helper.IsNonEmptyString(CustomerContactName)) {
        Alert.alert('', translate(keys.shoppingCart.validation_customer_name));
        return false;
    }

    return true;
};
