/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react/jsx-fragments */
import {
    View,
    TouchableOpacity,
    Keyboard,
    FlatList,
    StyleSheet
} from 'react-native';
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector, batch } from 'react-redux';
import Toast from 'react-native-toast-message';
import {
    BaseLoading,
    FieldInput,
    PickerLocation,
    MyText,
    Icon,
    LoyaltyScanner
} from '@components';
import { COLORS } from '@styles';
import { helper, storageHelper } from '@common';
import { DEVICE, ENUM, STORAGE_CONST, constants } from '@constants';
import { keys, translate } from '@translate';
import {
    getDataDistrict,
    getDataProvince,
    getWard
} from '../../Location/action';
import RadioGender from '../../Detail/component/Radio/RadioGender';

import StoreInfo from './StoreInfo';
import useTheme from '../useTheme';
import {
    checkFeeShipping,
    getShippingRoute,
    resetCustomerInfo,
    resetDeliveryInfo,
    setDeliveryAddress,
    setDeliveryContact,
    setTempPhoneNumber,
    updateCustomerInfo,
    updateDeliveryAddress
} from '../action';
import { getCompanyByTax, getCustomerByPhone } from '../../ShoppingCart/action';
import { useDelivery, useDeliveryDispatch } from '../context/DeliveryProvider';
import { INFO_KEY } from './HomeDelivery';
import CheckBox from '../components/CheckBox';
import { pharmacyState } from '../state';
import useThemedStyles from '../useThemdStyles';
import RadioItem from '../components/RadioItem';
import InputField from '../components/InputField';
import { useBrandCheck } from '../hooks';
import AddressManager from '../../AddressManager';

const ListHomeDelivery = ({
    shopCart,
    handleDataShippingRoute,
    isDisableCustomerPhone,
    inputAddressFocus
}) => {
    const theme = useTheme();
    const COLOR = theme.colors;

    const deliveryDispatch = useDeliveryDispatch();
    const { provinceID } = useSelector((state) => state.userReducer);
    const { deliveryAddress, deliveryContact } = useDelivery();

    const [stateShippingRoute, setStateShippingRoute] = useState({
        dataShippingRoute: [],
        isFetching: false,
        description: '',
        isError: false
    });
    const [dataWard, setDataWard] = useState([]);
    const [indexPager, setIndexPager] = useState(0);
    const [isWardFetching, setIsWardFetching] = useState(false);
    const [isShowMethod, setIsShowMethod] = useState(true);
    const [isCompany, setIsCompany] = useState(false);
    const [isDuplicateCustomerInfo, setIsDuplicateCustomerInfo] =
        useState(true);
    const pickerRef = useRef(null);

    useEffect(() => {
        setIsDuplicateCustomerInfo(!isDisableCustomerPhone)
    }, [isDisableCustomerPhone])

    const dispatch = useDispatch();
    const { dataDistrict, dataProvince, stateDistrict } = useSelector(
        (state) => state.locationReducer
    );
    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );
    const { typeDelivery } = useSelector((state) => state._pharmacyReducer);
    const handleApiGetDistrict = (provinceId) => {
        dispatch(getDataDistrict(provinceId));
    };

    const handleApiGetWard = ({ provinceId, districtId }) => {
        setIsWardFetching(true);
        getWard(provinceId, districtId)
            .then((wards) => {
                setIsWardFetching(false);
                setDataWard(wards);
            })
            .catch((error) => {
                setIsWardFetching(false);
                setDataWard([]);
                console.log(error);
            });
    };
    const handleGetShippingRoute = () => {
        setStateShippingRoute({
            ...stateShippingRoute,
            isFetching: true,
            description: '',
            isError: false
        });
        deliveryDispatch(resetDeliveryInfo());
        const DeliveryInfo = {
            DeliveryTypeID: typeDelivery,
            DeliveryDistrictID: deliveryAddress.districtID,
            DeliveryProvinceID: deliveryAddress.provinceID,
            DeliveryWardID: deliveryAddress.wardID,
            DeliveryAddress: deliveryContact.contactAddress
        };
        const body = {
            moduleID,
            languageID,
            loginStoreId: storeID,
            cartRequest: shopCart,
            deliveryInfo: DeliveryInfo
        };
        getShippingRoute(body)
            .then((data) => {
                setStateShippingRoute({
                    ...stateShippingRoute,
                    dataShippingRoute: data,
                    isFetching: false,
                    isError: false,
                    description: ''
                });
            })
            .catch((msgError) => {
                setStateShippingRoute({
                    ...stateShippingRoute,
                    dataShippingRoute: [],
                    isFetching: false,
                    isError: true,
                    description: msgError
                });
            });
    };
    const handleApiGetContactInfo = (phoneNumber) => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            handleCheckFeeShipping(phoneNumber)
            getCustomerByPhone(phoneNumber).then((info) => {
                const { gender, customerName, customerAddress } = info;
                deliveryDispatch(
                    setDeliveryContact({
                        gender,
                        contactPhone: phoneNumber,
                        contactName: customerName,
                        contactAddress: customerAddress
                    })
                );
            });
        }
    };
    const handleGetOldFriendInfo = () => {
        if (deliveryContact.contactPhone) {
            handleApiGetContactInfo(deliveryContact.contactPhone);
        } else {
            storageHelper
                .getItem(STORAGE_CONST.CUSTOMER_INFO)
                .then((result) => {
                    if (helper.IsNonEmptyString(result)) {
                        const dataTopInfo = JSON.parse(result);
                        const customerInfo = dataTopInfo.find((ele) =>
                            helper.IsEmptyString(ele.taxID)
                        );
                        if (customerInfo) {
                            const {
                                gender,
                                customerName,
                                customerAddress,
                                customerPhone
                            } = customerInfo;

                            deliveryDispatch(
                                setDeliveryContact({
                                    gender,
                                    contactPhone: customerPhone,
                                    contactName: customerName,
                                    contactAddress: customerAddress
                                })
                            );
                        }
                    }
                })
                .catch((error) => {
                    console.log('getOldCustomerInfo error', error);
                });
        }
    };

    const handleUpdateContactInfo = ({ value, key }) => {
        deliveryDispatch(
            setDeliveryContact({
                ...deliveryContact,
                [key]: value
            })
        );
    };
    useEffect(() => {
        batch(() => {
            dispatch(getDataProvince());
            dispatch(getDataDistrict(provinceID));
        });
        deliveryDispatch(
            setDeliveryAddress({
                wardID: 0,
                districtID: 0,
                provinceID
            })
        );
    }, [provinceID]);

    useEffect(() => {
        if (!helper.IsEmptyObject(shopCart)) {
            const newDeliveryInfoRequest = shopCart.SaleOrderDetails?.find(
                (saleOrder) =>
                    saleOrder.DeliveryInfoRequest.DeliveryProvinceID > 0 &&
                    saleOrder.DeliveryInfoRequest.DeliveryDistrictID > 0
            );
            if (!helper.IsEmptyObject(newDeliveryInfoRequest)) {
                const {
                    DeliveryProvinceID,
                    DeliveryWardID,
                    DeliveryDistrictID
                } = newDeliveryInfoRequest.DeliveryInfoRequest;
                deliveryDispatch(
                    setDeliveryAddress({
                        provinceID: DeliveryProvinceID,
                        districtID: DeliveryDistrictID,
                        wardID: DeliveryWardID
                    })
                );
                handleApiGetWard({
                    provinceId: DeliveryProvinceID,
                    districtId: DeliveryDistrictID
                });
            }
        }
    }, [shopCart]);

    const handleCheckFeeShipping = async (phoneNumber) => {
        if (isDuplicateCustomerInfo && shopCart?.CustomerMembershipBO?.IsCheckCustomerMenbership) {
            const customerMembership = await dispatch(checkFeeShipping(phoneNumber))
            if (!helper.IsEmptyObject(customerMembership)) {
                shopCart.CustomerMembershipBO = customerMembership
            }
        }
    }

    const handleFetchRoute = () => {
        if (deliveryAddress.wardID > 0 && !helper.IsEmptyObject(shopCart)) {
            inputAddressFocus.current = false
            handleGetShippingRoute();
        }
    }

    useEffect(() => {
        if (deliveryAddress.wardID > 0 && !helper.IsEmptyObject(shopCart)) {
            handleGetShippingRoute();
        }
    }, [deliveryAddress.wardID, shopCart]);

    useEffect(() => {
        if (helper.IsNonEmptyArray(stateShippingRoute.dataShippingRoute)) {
            let listDeliveryDetail = [];
            stateShippingRoute.dataShippingRoute.forEach((item) => {
                if (helper.IsNonEmptyArray(item.DeliveryDetails)) {
                    item.DeliveryDetails.forEach((deliveryDetail) => {
                        if (
                            helper.IsNonEmptyArray(
                                deliveryDetail.ProductDetailInfoList
                            )
                        ) {
                            listDeliveryDetail = [
                                ...listDeliveryDetail,
                                ...deliveryDetail.ProductDetailInfoList
                            ];
                        }
                    });
                }
                return listDeliveryDetail;
            });
            handleDataShippingRoute(listDeliveryDetail);
        }
    }, [stateShippingRoute.dataShippingRoute]);
    useEffect(() => {
        if (isDuplicateCustomerInfo) {
            const { gender, contactPhone, contactName, contactAddress } =
                deliveryContact;
            dispatch(
                updateCustomerInfo({
                    Gender: gender,
                    CustomerPhone: '',
                    CustomerName: '',
                    CustomerAddress: '',
                    TaxID: '',
                    ContactName: contactName,
                    ContactPhone: contactPhone,
                    ContactAddress: contactAddress,
                    TempCartPhoneNumber: '',
                    TempCartContactPhone: contactPhone
                })
            );
        }
    }, [deliveryContact, isDuplicateCustomerInfo]);

    useEffect(() => {
        dispatch(
            updateCustomerInfo({
                IsCompany: isCompany
            })
        );
    }, [isCompany]);

    return (
        <View>
            {/* <PickerLocation
                dataProvince={{
                    data: dataProvince,
                    id: 'provinceID',
                    value: 'provinceName'
                }}
                dataDistrict={{
                    data: dataDistrict,
                    id: 'districtID',
                    value: 'districtName'
                }}
                dataWard={{
                    data: dataWard,
                    id: 'wardID',
                    value: 'wardName'
                }}
                wardID={deliveryAddress.wardID}
                districtID={deliveryAddress.districtID}
                provinceID={deliveryAddress.provinceID}
                onSelectProvince={(item) => {
                    deliveryDispatch(
                        setDeliveryAddress({
                            provinceID: item.provinceID,
                            districtID: 0,
                            wardID: 0
                        })
                    );

                    setIndexPager((idx) => idx + 1);
                    handleApiGetDistrict(item.provinceID);
                }}
                onSelectDistrict={(item) => {
                    deliveryDispatch(
                        setDeliveryAddress({
                            ...deliveryAddress,
                            districtID: item.districtID,
                            wardID: 0
                        })
                    );

                    setIndexPager((idx) => idx + 1);
                    handleApiGetWard({
                        provinceId: deliveryAddress.provinceID,
                        districtId: item.districtID
                    });
                }}
                onSelectWard={(item) => {
                    if (item.wardID !== deliveryAddress.wardID) {
                        deliveryDispatch(
                            setDeliveryAddress({
                                ...deliveryAddress,
                                wardID: item.wardID
                            })
                        );
                    }
                }}
                indexPager={indexPager}
                onShowPicker={setIndexPager}
                updatePager={setIndexPager}
                isShowIndicator={isWardFetching || stateDistrict.isFetching}
            /> */}
            <View style={{ alignItems: "center" }}>
                <AddressManager
                    ref={pickerRef}
                    wardID={deliveryAddress.wardID}
                    districtID={deliveryAddress.districtID}
                    provinceID={deliveryAddress.provinceID}
                    onChangeProvince={(item) => {
                        deliveryDispatch(updateDeliveryAddress({
                            provinceID: item.provinceID,
                            districtID: 0,
                            wardID: 0
                        }));
                    }}

                    onChangeDistrict={(item) => {
                        deliveryDispatch(updateDeliveryAddress({
                            districtID: item.districtID,
                            wardID: 0
                        }));
                    }}

                    onChangeWard={(item) => {
                        if (item.wardID !== deliveryAddress.wardID) {
                            deliveryDispatch(updateDeliveryAddress({
                                districtID: item.districtID,
                                wardID: item.wardID
                            }));
                        }
                    }}
                    enableDistrictSelection={helper.configApplyLocation()}
                />
            </View>

            <>
                <ContactInfo
                    info={deliveryContact}
                    setInfo={handleUpdateContactInfo}
                    getContactInfo={handleApiGetContactInfo}
                    getOldCustomerInfo={handleGetOldFriendInfo}
                    inputAddressFocus={inputAddressFocus}
                    handleGetShippingRoute={handleFetchRoute}
                />
                {!isDisableCustomerPhone && <View
                    style={{
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}>
                    <CheckBox
                        style={{ marginVertical: 5 }}
                        isCheck={isDuplicateCustomerInfo}
                        label="Thông tin giao hàng là thông tin của khách hàng"
                        onCheck={() => {
                            setIsDuplicateCustomerInfo(!isDuplicateCustomerInfo);
                        }}
                    />
                </View>}
                {!isDuplicateCustomerInfo && (
                    <View>
                        <View
                            style={{
                                paddingHorizontal: 10,
                                backgroundColor: isDuplicateCustomerInfo
                                    ? COLORS.bgE0E0E0
                                    : '#e82063',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                height: 35,
                                marginBottom: 5
                            }}>
                            <MyText
                                style={{
                                    color: 'white',
                                    fontWeight: 'bold',
                                    marginLeft: 5
                                }}
                                text="Thông tin khách hàng"
                            />
                        </View>
                        <CustomerInfo
                            isCompany={isCompany}
                            setIsCompany={setIsCompany}
                            isDisableCustomerPhone={isDisableCustomerPhone}
                            shopCart={shopCart}
                        />
                    </View>
                )}
                {deliveryAddress.wardID > 0 && (
                    <>
                        <TouchableOpacity
                            onPress={() => {
                                setIsShowMethod(!isShowMethod);
                            }}
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                backgroundColor: '#E9EFF3',
                                height: 30,
                                alignItems: 'center'
                            }}>
                            <MyText
                                style={{
                                    marginLeft: 5,
                                    color: COLOR.primary,
                                    fontWeight: 'bold'
                                }}
                                text="Hình thức giao"
                            />
                            <Icon
                                iconSet="Ionicons"
                                name={
                                    isShowMethod
                                        ? 'caret-up-sharp'
                                        : 'caret-down-sharp'
                                }
                                color={COLOR.primary}
                                size={16}
                                style={{ marginRight: 5 }}
                            />
                        </TouchableOpacity>
                        {isShowMethod && (
                            <BaseLoading
                                isLoading={stateShippingRoute.isFetching}
                                textLoadingError={
                                    stateShippingRoute.description
                                }
                                isError={stateShippingRoute.isError}
                                onPressTryAgains={handleGetShippingRoute}
                                content={
                                    <FlatList
                                        style={{
                                            marginBottom: 5
                                        }}
                                        data={
                                            stateShippingRoute.dataShippingRoute
                                        }
                                        keyExtractor={(item, index) =>
                                            `${index}`
                                        }
                                        renderItem={({ item }) => (
                                            <StoreInfo deliveryStore={item} />
                                        )}
                                        stickySectionHeadersEnabled={false}
                                        alwaysBounceVertical={false}
                                        bounces={false}
                                        scrollEventThrottle={16}
                                    />
                                }
                            />
                        )}
                    </>
                )}
            </>
        </View>
    );
};

const ContactInfo = ({ info, setInfo, getContactInfo, getOldCustomerInfo, inputAddressFocus, handleGetShippingRoute }) => {
    const { gender, contactPhone, contactName, contactAddress } = info;
    return (
        <View style={{ alignItems: 'center' }}>
            <View
                style={{
                    width: constants.width - 20,
                    flexDirection: 'row',
                    paddingVertical: 4,
                    justifyContent: 'space-between'
                }}>
                <RadioGender
                    gender={gender}
                    onSwitchGender={(value) => {
                        setInfo({
                            value,
                            key: INFO_KEY.GENDER
                        });
                    }}
                />
                <TouchableOpacity
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                    onPress={getOldCustomerInfo}>
                    <MyText
                        style={{
                            color: COLORS.txtFFA500,
                            textDecorationLine: 'underline',
                            fontWeight: 'bold'
                        }}
                        text={translate('detail.old_customer')}
                    />
                </TouchableOpacity>
            </View>

            <FieldInput
                styleInput={{
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    marginVertical: 5,
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingVertical: 8
                }}
                placeholder={translate(
                    'detail.text_input_phone_number_contact'
                )}
                value={contactPhone}
                onChangeText={(text) => {
                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                    const isValidate = regExpPhone.test(text) || text === '';
                    if (isValidate) {
                        setInfo({
                            value: text,
                            key: INFO_KEY.PHONE
                        });
                    }
                }}
                keyboardType="numeric"
                returnKeyType="done"
                onBlur={() => getContactInfo(contactPhone)}
                blurOnSubmit
                width={constants.width - 20}
                height={40}
                clearText={() => {
                    setInfo({
                        value: '',
                        key: INFO_KEY.PHONE
                    });
                }}
            />

            <FieldInput
                styleInput={{
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    marginVertical: 5,
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingVertical: 8
                }}
                placeholder={translate('detail.text_input_name_contact')}
                value={contactName}
                onChangeText={(text) => {
                    if (helper.isValidateCharVN(text)) {
                        setInfo({
                            value: text,
                            key: INFO_KEY.NAME
                        });
                    }
                }}
                returnKeyType="default"
                width={constants.width - 20}
                height={40}
                clearText={() => {
                    setInfo({
                        value: '',
                        key: INFO_KEY.NAME
                    });
                }}
                maxLength={50}
            />

            <FieldInput
                styleInput={{
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    marginVertical: 5,
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.bgFFFFFF,
                    justifyContent: 'center',
                    paddingVertical: 8
                }}
                textAlignVertical="center"
                underlineColorAndroid="transparent"
                placeholder={translate('detail.text_input_address_contact')}
                value={contactAddress}
                onChangeText={(text) => {
                    if (helper.isValidateCharVN(text)) {
                        setInfo({
                            value: text,
                            key: INFO_KEY.ADDRESS
                        });
                    }
                }}
                returnKeyType="default"
                blurOnSubmit
                onSubmitEditing={() => {
                    Keyboard.dismiss();
                }}
                width={constants.width - 20}
                multiline
                height={40}
                clearText={() => {
                    setInfo({
                        value: '',
                        key: INFO_KEY.ADDRESS
                    });
                }}
                maxLength={300}
                onBlur={handleGetShippingRoute}
                onFocus={() => { inputAddressFocus.current = true; }}
            />
        </View>
    );
};

const CustomerInfo = ({ isCompany, setIsCompany, isDisableCustomerPhone, shopCart }) => {
    const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
    const styles = useThemedStyles(style);
    const theme = useTheme();
    const COLOR = theme.colors;
    const dispatch = useDispatch();
    const { customerInfo } = useSelector((state) => state._pharmacyReducer);
    const { saleExpress, shoppingCart } = keys;
    const {
        Gender,
        CustomerPhone,
        CustomerName,
        CustomerAddress,
        TaxID,
        ContactName,
        ContactPhone,
        ContactAddress,
        TempCartPhoneNumber,
        TempCartContactPhone
    } = customerInfo;
    const getCompanyInfo = (taxID) => {
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = isValidateTax10 || isValidateTax14;
        if (isValidate) {
            getCompanyByTax(taxID).then((info) => {
                dispatch(
                    updateCustomerInfo({
                        Gender: info.gender,
                        CustomerPhone: info.customerPhone,
                        CustomerName: info.customerName,
                        CustomerAddress: info.customerAddress,
                        TaxID: taxID,
                        ContactName: '',
                        ContactPhone: '',
                        ContactAddress: '',
                        TempCartPhoneNumber: info.customerPhone,
                        TempCartContactPhone: ''
                    })
                );
            });
        } else {
            Toast.show({
                type: 'error',
                text1: translate(shoppingCart.validation_tax)
            });
        }
    };
    const handleSwitchGender = (gender) => {
        // true -> Anh | false -> Chị
        dispatch(updateCustomerInfo({ Gender: gender }));
    };
    const handleCheckFeeShipping = async (phoneNumber) => {
        if (shopCart?.CustomerMembershipBO?.IsCheckCustomerMenbership) {
            const customerMembership = await dispatch(checkFeeShipping(phoneNumber))
            if (!helper.IsEmptyObject(customerMembership)) {
                shopCart.CustomerMembershipBO = customerMembership
            }
        }
    }
    const getCustomerInfo = (phoneNumber) => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            // checkAppLoyalty(phoneNumber);
            handleCheckFeeShipping(phoneNumber)
            getCustomerByPhone(phoneNumber).then((info) => {
                const customerPhone = phoneNumber;
                const customerName = info.customerName || CustomerName;
                const customerAddress = info.customerAddress || CustomerAddress;

                dispatch(
                    updateCustomerInfo({
                        Gender: info.gender,
                        CustomerPhone: '',
                        CustomerName: '',
                        CustomerAddress: '',
                        TaxID: '',
                        ContactName: customerName,
                        ContactPhone: customerPhone,
                        ContactAddress: customerAddress,
                        TempCartPhoneNumber: '',
                        TempCartContactPhone: customerPhone
                    })
                );
            });
        }
    };
    const getOldCustomerInfo = () => {
        if (ContactPhone) {
            getCustomerInfo(ContactPhone);
        } else {
            console.log('getOldCustomerInfo ssssss');

            storageHelper
                .getItem(STORAGE_CONST.CUSTOMER_INFO)
                .then((result) => {
                    if (helper.IsNonEmptyString(result)) {
                        const dataTopInfo = JSON.parse(result);
                        const customerInfoLocal = dataTopInfo.find((ele) =>
                            helper.IsEmptyString(ele.taxID)
                        );
                        if (customerInfoLocal) {
                            const {
                                customerPhone,
                                customerName,
                                customerAddress,
                                contactAddress,
                                contactName,
                                contactPhone,
                                gender
                            } = customerInfoLocal;
                            dispatch(
                                updateCustomerInfo({
                                    Gender: gender,
                                    ContactName: customerName || contactName,
                                    ContactPhone: customerPhone || contactPhone,
                                    ContactAddress:
                                        customerAddress || contactAddress,
                                    TempCartContactPhone: customerPhone || contactPhone

                                })
                            );
                            dispatch(setTempPhoneNumber(customerPhone || contactPhone));
                        }
                    }
                })
                .catch((error) => {
                    console.log('getOldCustomerInfo error', error);
                });
        }
    };
    const getContactInfo = (phoneNumber) => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            // checkAppLoyalty(phoneNumber);
            handleCheckFeeShipping(phoneNumber)
            getCustomerByPhone(phoneNumber).then((info) => {
                dispatch(
                    updateCustomerInfo({
                        Gender: info.gender,
                        CustomerPhone: '',
                        CustomerName: '',
                        CustomerAddress: '',
                        TaxID: '',
                        ContactName: info.customerName,
                        ContactPhone: phoneNumber,
                        ContactAddress: info.customerAddress,
                        TempCartPhoneNumber: '',
                        TempCartContactPhone: phoneNumber
                    })
                );
            });
        }
    };
    const handleFinishScanLoyalty = (info) => {
        // setDataVerifyInfo(info); để sau
        handleCheckFeeShipping(info.customerPhone)
        dispatch(
            updateCustomerInfo({
                Gender: info.gender,
                ContactName: info.customerName,
                ContactPhone: info.customerPhone,
                CustomerPhone: info.customerPhone,
                CustomerAddress: info.customerAddress,
                ContactAddress: info.customerAddress
            })
        );
    };
    return (
        <View style={[styles.pageContainer, { paddingHorizontal: 10 }]}>
            <CheckBox
                style={{ marginVertical: 10 }}
                isCheck={isCompany}
                label={translate(shoppingCart.customer_print_company_bill)}
                onCheck={() => {
                    setIsCompany((prev) => !prev);
                    if (!isDisableCustomerPhone) {
                        dispatch(resetCustomerInfo());
                    } else {
                        dispatch(
                            updateCustomerInfo({
                                ...pharmacyState.customerInfo,
                                CustomerPhone,
                                ContactPhone: CustomerPhone
                            })
                        );
                    }
                }}
            />
            {isCompany && (
                <View>
                    <InputField
                        isRequired
                        label={translate(saleExpress.tax_id)}
                        placeholder={translate(saleExpress.placeholder_tax_id)}
                        keyboardType={
                            DEVICE.isIOS
                                ? 'numbers-and-punctuation'
                                : 'visible-password'
                        }
                        value={TaxID}
                        onChangeText={(text) => {
                            const regExpTax = new RegExp(/^[0-9-KL]{0,14}$/);
                            const isValidate =
                                regExpTax.test(text) || text === '';
                            if (isValidate) {
                                dispatch(
                                    updateCustomerInfo({
                                        TaxID: text
                                    })
                                );
                            }
                        }}
                        onClear={() => {
                            dispatch(
                                updateCustomerInfo({
                                    TaxID: ''
                                })
                            );
                        }}
                        onBlur={() => {
                            if (TaxID.length > 0) {
                                getCompanyInfo(TaxID);
                            }
                        }}
                    />
                    <InputField
                        isRequired
                        label={translate(saleExpress.company_name)}
                        placeholder={translate(
                            shoppingCart.placeholder_customer_company_name
                        )}
                        value={CustomerName}
                        onChangeText={(text) => {
                            if (helper.isValidateCharVN(text)) {
                                dispatch(
                                    updateCustomerInfo({
                                        CustomerName: text
                                    })
                                );
                            }
                        }}
                        onClear={() => {
                            dispatch(
                                updateCustomerInfo({
                                    CustomerName: ''
                                })
                            );
                        }}
                    />
                    <InputField
                        isRequired
                        label={translate(saleExpress.company_address)}
                        placeholder={translate(
                            saleExpress.placeholder_company_address
                        )}
                        value={CustomerAddress}
                        onChangeText={(text) => {
                            if (helper.isValidateCharVN(text)) {
                                dispatch(
                                    updateCustomerInfo({
                                        CustomerAddress: text
                                    })
                                );
                            }
                        }}
                        onClear={() => {
                            dispatch(
                                updateCustomerInfo({
                                    CustomerAddress: ''
                                })
                            );
                        }}
                    />
                    <InputField
                        label={translate(saleExpress.company_phone)}
                        placeholder={translate(
                            saleExpress.placeholder_company_phone
                        )}
                        value={CustomerPhone || TempCartPhoneNumber}
                        onChangeText={(text) => {
                            const regExpPhone = new RegExp(/^[0]\d{0,10}$/);
                            const isValidate =
                                regExpPhone.test(text) || text === '';
                            if (isValidate) {
                                dispatch(
                                    updateCustomerInfo({
                                        CustomerPhone: text,
                                        ContactPhone: text,
                                        TempCartContactPhone:
                                            text,
                                        TempCartPhoneNumber:
                                            text
                                    })
                                );
                            }
                        }}
                        onClear={() => {
                            dispatch(
                                updateCustomerInfo({
                                    CustomerPhone: '',
                                    ContactPhone: '',
                                    TempCartContactPhone:
                                        '',
                                    TempCartPhoneNumber: ''
                                })
                            );
                        }}
                        keyboardType="numeric"
                    />
                </View>
            )}
            <View style={styles.rowRadio}>
                <RadioItem
                    isCheck={Gender}
                    disabled={Gender}
                    style={{ marginRight: 40 }}
                    title={translate(saleExpress.mr)}
                    onPressItem={() => handleSwitchGender(true)}
                />
                <RadioItem
                    isCheck={!Gender}
                    title={translate(saleExpress.ms)}
                    disabled={!Gender}
                    onPressItem={() => handleSwitchGender(false)}
                />
                <TouchableOpacity
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginLeft: 'auto'
                    }}
                    onPress={() => getOldCustomerInfo(customerInfo)}>
                    <MyText
                        text={translate(shoppingCart.old_customer)}
                        style={{
                            color: COLOR.primary,
                            textDecorationLine: 'underline',
                            fontWeight: 'bold'
                        }}
                    />
                </TouchableOpacity>
            </View>
            <InputField
                keyboardType="numeric"
                label={
                    isCompany
                        ? translate(saleExpress.contact_phone)
                        : translate(saleExpress.phone)
                }
                placeholder={translate(saleExpress.placeholder_phone)}
                value={ContactPhone || TempCartContactPhone}
                onBlur={() => {
                    getContactInfo(ContactPhone)
                    dispatch(setTempPhoneNumber(ContactPhone));
                }}
                onChangeText={(text) => {
                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                    const isValidate = regExpPhone.test(text) || text === '';
                    if (isValidate) {
                        dispatch(
                            updateCustomerInfo({
                                ContactPhone: text,
                                TempCartContactPhone: text

                            })
                        );
                    }
                    if (text == '') {
                        dispatch(setTempPhoneNumber(''));
                    }
                }}
                onClear={() => {
                    dispatch(
                        updateCustomerInfo({
                            ContactPhone: '',
                            TempCartContactPhone: ""
                        })
                    );
                    dispatch(setTempPhoneNumber(''));
                }}
                editable={!isDisableCustomerPhone}
                RightComponent={
                    <LoyaltyScanner onFinishScan={handleFinishScanLoyalty} />
                }
            />
            <InputField
                isRequired={isAva}
                label={
                    isCompany
                        ? translate(saleExpress.contact_name)
                        : translate(saleExpress.full_name)
                }
                placeholder={translate(saleExpress.placeholder_customer_name)}
                value={ContactName}
                onChangeText={(text) => {
                    if (helper.isValidateCharVN(text)) {
                        dispatch(
                            updateCustomerInfo({
                                ContactName: text
                            })
                        );
                    }
                }}
                onClear={() => {
                    dispatch(
                        updateCustomerInfo({
                            ContactName: ''
                        })
                    );
                }}
            />
            <InputField
                label={
                    isCompany
                        ? translate(saleExpress.contact_address)
                        : translate(saleExpress.address)
                }
                placeholder={translate(saleExpress.placeholder_address)}
                value={ContactAddress}
                onChangeText={(text) => {
                    if (helper.isValidateCharVN(text)) {
                        dispatch(
                            updateCustomerInfo({
                                ContactAddress: text
                            })
                        );
                    }
                }}
                onClear={() => {
                    dispatch(
                        updateCustomerInfo({
                            ContactAddress: ''
                        })
                    );
                }}
            />
        </View>
    );
};

export default ListHomeDelivery;

const style = (theme) => {
    const COLOR = theme.colors;
    return StyleSheet.create({
        bar: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            overflow: 'hidden',
            paddingBottom: 3
        },
        borderStyle: {
            backgroundColor: COLOR.bgCart
        },
        bottomBar: {
            backgroundColor: COLOR.white,
            flexDirection: 'row',
            justifyContent: 'center'
        },
        button: {
            backgroundColor: COLOR.white,
            borderColor: COLOR.primary,
            borderRadius: constants.getSize(10),
            borderWidth: 2,
            height: constants.getSize(40),
            marginHorizontal: 15,
            marginVertical: 10,
            width: constants.width / 2 - 50
        },

        pageContainer: {
            backgroundColor: '#faf7f8',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            flex: 1
        },

        rowRadio: {
            flexDirection: 'row',
            marginBottom: 10
        }
    });
};
