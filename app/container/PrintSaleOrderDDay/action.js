import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_PRINT_PRE_ORDER
} = API_CONST;

export const actionPrintSaleOrderDDay = {

};

export const getDataPrintSaleOrderDDay = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                outputStoreId: getState().userReducer.storeID,
                speciaSaleProgramIdList: data.speciaSaleProgramIdList,
                orderTypeIdList: data.orderTypeIdList
            };
            apiBase(API_PRINT_PRE_ORDER, METHOD.POST, body)
                .then((response) => {
                    console.log("getDataPrintSaleOrderDDay success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    } else {
                        reject("Không lấy được thông tin mẫu in!")
                    }
                })
                .catch((error) => {
                    console.log("getDataPrintSaleOrderDDay error", error);
                    reject(error.msgError);
                });
        });
    };
};

