import React, { useState, useEffect } from "react";
import { View, Alert, SafeAreaView, Keyboard, StyleSheet } from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    show<PERSON><PERSON><PERSON>,
    hide<PERSON><PERSON>UI,
    Button
} from "@components";
import { constants } from "@constants";
import { helper, getBase64PdfByUrl } from "@common";
import PrintReport from "../../../SaleOrderManager/component/PrintReport/index";
import Report from "../../../SaleOrderManager/component/PrintReport/Report";
import * as actionManagerSOCreator from "../../../SaleOrderManager/action";
import * as actionSaleOrderCreator from "../../../SaleOrderPayment/action";
import * as actionInsuranceAirtimeServiceCreator from "../../action";
import { translate } from "@translate";
import { COLORS } from "@styles";

const PrintCertificateNew = ({
    statePrinter,
    printerCommon,
    actionSaleOrder,
    defaultReport,
    userInfo,
    actionInsuranceAirtimeService,
    route,
    navigation,
    itemCatalog,
}) => {
    const { data } = route.params ?? {};
    const { SALEORDERID } = data ?? "";
    const [reportCommon, setReportCommon] = useState({});
    const [dataPrint, setDataPrint] = useState("");

    useEffect(() => {
        getDataBeforePrint();
    }, []);

    const getDataBeforePrint = () => {
        showBlockUI();
        const data = {
            saleOrderID: SALEORDERID,
        };
        actionInsuranceAirtimeService
            .getDataInfo(data)
            .then((response) => {
                hideBlockUI();
                setDataPrint(response);
            })
            .catch((error) => {
                console.error("Error occurred while fetching Base64 data:", error);
                Alert.alert(
                    translate("common.notification_uppercase"),
                    error.msgError,
                    [
                        {
                            text: translate("common.btn_accept"),
                            onPress: () => {
                                hideBlockUI();
                                navigation.goBack();
                            },
                        },
                    ]
                );
            });
    };

    const getReportPrinter = () => {
        actionSaleOrder.getReportPrinter("8");
    };

    const effectChangeReport = () => {
        setReportCommon(defaultReport.common);
    };

    useEffect(getReportPrinter, []);

    useEffect(effectChangeReport, [defaultReport]);

    const onCheckContent = () => {
        Keyboard.dismiss();
        getPrintContent();
    };

    const getPrintContent = async () => {
        try {
            showBlockUI();
            const inputData = dataPrint?.data;
            const printType = dataPrint?.type;
            if (printType == "pdfURL") {
                const base64PDF = await getBase64PdfByUrl(inputData);
                onPrintBill(base64PDF);
            } else if (printType == "base64") {
                onPrintBill(inputData);
            } else {
                Alert.alert(
                    translate("common.notification_uppercase"),
                    "Không tìm thấy mẫu in phù hợp liên hệ IT để được hổ trợ!",
                    [
                        {
                            text: translate("common.btn_accept"),
                            onPress: () => {
                                hideBlockUI();
                                navigation.goBack();
                            },
                        },
                    ]
                );
            }
        } catch (msgError) {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI,
                },
            ]);
        }
    };

    const onPrintBill = (data) => {
        const requestAPI = getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequest(requestAPI);
        } else {
            hideBlockUI();
        }
    };

    const getPrintRequestAPI = (content) => {
        const requestAPI = [];
        const report = reportCommon;
        if (!helper.IsEmptyObject(report)) {
            for (let i = 0; i < 1; i++) {
                const printService = getPrintService(report, content);
                requestAPI.push(printService);
            }
        }
        return requestAPI;
    };

    const getPrintService = (report, content) => {
        const AirtimeServiceGroupID = itemCatalog?.AirtimeServiceGroupID;

        let printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: true,
            bolShrinkToMargin: false,
            strBase64: content,
        };

        if (AirtimeServiceGroupID == 19) {
            printerConfig.strPaperSize = "A5";
        }
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator
                .printBillVoucher(formBody)
                .then((result) => {
                    resolve(result);
                })
                .catch((msgError) => {
                    reject(msgError);
                });
        });
    };

    const printAllRequest = (allPromise) => {
        Promise.all(allPromise)
            .then((result) => {
                console.log("PRINT RSULT", result);
                Alert.alert("", translate("saleOrderManager.print_success"), [
                    {
                        text: "OK",
                        style: "default",
                        onPress: hideBlockUI,
                    },
                ]);
            })
            .catch((msgError) => {
                Alert.alert(translate("common.notification_uppercase"), msgError, [
                    {
                        text: "OK",
                        style: "default",
                        onPress: hideBlockUI,
                    },
                ]);
            });
    };

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps={"always"}
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 10,
                    }}
                >
                    <PrintReport
                        title={translate("saleOrderManager.select_printer")}
                        statePrinter={statePrinter}
                        onTryAgains={getReportPrinter}
                        dataCommon={printerCommon}
                        renderItemCommon={({ item, index }) => (
                            <Report
                                key={`ReportCommon`}
                                info={item}
                                report={reportCommon}
                                onCheck={() => {
                                    setReportCommon(item);
                                }}
                            />
                        )}
                    />
                    <ButtonAction onPress={onCheckContent} />
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
};

const mapStateToProps = (state) => ({
    printerCommon: state.saleOrderPaymentReducer.printerCommon,
    defaultReport: state.saleOrderPaymentReducer.defaultReport,
    statePrinter: state.saleOrderPaymentReducer.statePrinter,
    userInfo: state.userReducer,
    itemCatalog: state.collectionReducer.itemCatalog,
});

const mapDispatchToProps = (dispatch) => ({
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionInsuranceAirtimeService: bindActionCreators(
        actionInsuranceAirtimeServiceCreator,
        dispatch
    ),
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(PrintCertificateNew);

const ButtonAction = ({ onPress }) => {
    return (
        <View
            style={{
                alignItems: "center",
                width: constants.width,
                paddingVertical: 10,
                marginTop: 10,
            }}
        >
            <Button
                text={"IN GIẤY CHỨNG NHẬN"}
                styleContainer={{
                    backgroundColor: COLORS.btn288AD6,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: 1,
                    borderRadius: 4,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                }}
                onPress={onPress}
            />
        </View>
    );
};

const styles = StyleSheet.create({});
