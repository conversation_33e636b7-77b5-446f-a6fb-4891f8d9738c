import { Alert, Linking } from "react-native";
import { debug, info, warn, error } from 'react-native-exception-handler';
import ImageResizer from 'react-native-image-resizer';
import NetInfo from '@react-native-community/netinfo';
import { getMacAddress } from 'react-native-device-info';
import { DEFAULT_VALUE } from "@dataValue";
import { constants, DEVICE, ENUM } from '@constants';
import { findStoreByIP } from "../../container/Network/action";
import { findStorePermission } from "../../container/UserInfo/action";
import * as dateHelper from '../date';
import * as uuid from 'uuid';

const { URL_FIND_IP, PRINT_CONFIG, regexEmail, ID_CARD_SIDE, ID_CARD_TYPE, THUMBNAIL_DESKTOP, PARTNER_ID, TYPE_PROFILE } = constants;
const { FILE_PATH: { INIT } } = ENUM;

/* data type */
export const isObject = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === Object;
};

export const setDefaultObject = (obj) => isObject(obj) ? obj : DEFAULT_VALUE.object;

export const isArray = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === Array;
};

export const setDefaultArray = (obj) => isArray(obj) ? obj : DEFAULT_VALUE.array;

export const isBoolean = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === Boolean;
};

export const setDefaultBoolean = (obj) => isBoolean(obj) ? obj : DEFAULT_VALUE.bool;

export const isFunction = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === Function;
};

export const setDefaultFunction = (obj) => isFunction(obj) ? obj : DEFAULT_VALUE.func;

export const isNumber = (obj) => {
    return (
        obj !== undefined &&
        obj !== null &&
        !Number.isNaN(obj) &&
        obj.constructor === Number
    );
};

export const setDefaultNumber = (obj) => isNumber(obj) ? obj : DEFAULT_VALUE.number;

export const isString = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === String;
};

export const setDefaultString = (obj) => isString(obj) ? obj : DEFAULT_VALUE.string;

export const deepCopy = (obj) => {
    const objStr = JSON.stringify(obj);
    return JSON.parse(objStr);
};

export const isJSON = (str) => {
    try {
        return JSON.parse(str) && !!str;
    } catch (e) {
        return false;
    }
};

export const isInstanced = (obj) => {
    if (obj === undefined || obj === null) {
        return false;
    }
    if (isArray(obj)) {
        return false;
    }
    if (isBoolean(obj)) {
        return false;
    }
    if (isFunction(obj)) {
        return false;
    }
    if (isNumber(obj)) {
        return false;
    }
    if (isObject(obj)) {
        return false;
    }
    if (isString(obj)) {
        return false;
    }
    return true;
};

export const isEmpty = (obj) => {
    for (const key in obj) {
        if (Object.hasOwnProperty.call(obj, key)) {
            return false;
        }
    }
    return true;
};

export const IsEmptyObject = (obj) => isEmpty(obj);
export const IsEmptyString = (str) => isString(str) && str.length === 0;
export const IsEmptyArray = (arr) => isArray(arr) && arr.length === 0;
export const IsNonEmptyString = (str) => isString(str) && str.trim().length > 0;
export const IsNonEmptyArray = (arr) => isArray(arr) && arr.length > 0;

export function IsValidateObject(object) {
    return object !== undefined && object !== null;
}

export const hasProperty = (object, property) => {
    return (
        IsValidateObject(object) &&
        Object.hasOwnProperty.call(object, property) &&
        IsValidateObject(object[property])
    );
};

/* navigation */

export const getParamScreen = (navigation) => {
    const { routes } = navigation.dangerouslyGetState();
    const { index } = navigation.dangerouslyGetState();
    return routes[index].params;
};

export const getNameScreen = (navigation) => {
    const { routes } = navigation.dangerouslyGetState();
    const { index } = navigation.dangerouslyGetState();
    return routes[index].name;
};

/* money */

export const formatMoney = (
    amount,
    decimalCount = 0,
    decimal = '.',
    thousands = ',',
    currencyStr = 'đ'
) => {
    try {
        decimalCount = Math.abs(decimalCount);
        decimalCount = Number.isNaN(decimalCount) ? 2 : decimalCount;
        const negativeSign = amount < 0 ? '-' : '';
        const i = parseInt(
            (amount = Math.abs(Number(amount) || 0).toFixed(decimalCount))
        ).toString();
        const j = i.length > 3 ? i.length % 3 : 0;
        return (
            negativeSign +
            (j ? i.substr(0, j) + thousands : '') +
            i.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) +
            (decimalCount
                ? decimal +
                Math.abs(amount - i)
                    .toFixed(decimalCount)
                    .slice(2)
                : '') +
            currencyStr
        );
    } catch (e) {
        return `0${currencyStr}`;
    }
};

/* timer */

export const countDownTimer = (timeLeft, timeInterval, update, complete) => {
    const start = new Date().getTime();
    const interval = setInterval(() => {
        const now = timeLeft * 1000 - (new Date().getTime() - start);
        if (now <= 0) {
            clearInterval(interval);
            return complete();
        } else {
            const count = Math.floor(now / 1000);
            return update(count);
        }
    }, timeInterval);
    return interval;
};

export const sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

/* uuid */

export const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 || 0;
        const v = c === 'x' ? r : (r && 0x3) || 0x8;
        return v.toString(16);
    });
};

export const stringToUuid = (str) => {
    str = str.replace('-', '');
    return 'xxxxxxxx-xxxx-4xxx-xxxx-xxxxxxxxxxxx'.replace(/[x]/g, (c, p) => {
        return str[p % str.length];
    });
};

/* timer */

export const convertLocalIdentifierToAssetLibrary = (localIdentifier, ext) => {
    const hash = localIdentifier.split('/')[0];
    return `assets-library://asset/asset.${ext}?id=${hash}&ext=${ext}`;
};

export const scaleImage = (image) => {
    // eslint-disable-next-line no-shadow
    const { width, height } = image;
    const max = 2048;
    let w = width;
    let h = height;
    if (width > height) {
        // 'landscape'
        if (w > max) {
            w = max;
            h = (max * height) / width;
        }
    } else if (width < height) {
        // 'portrait';
        if (h > max) {
            h = max;
            w = (max * width) / height;
        }
    } else {
        // orientation = 'event';
        w = max;
        h = (max * height) / width;
    }
    return Object.assign(image, { width: w, height: h });
};

/* string */

export const trim = (str) => {
    if (isString(str)) {
        return str.trim();
    }
    return str;
};

export const removeAccent = (str) => {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    return str;
};

/* logger */
export const LoggerDebug = function (...args) {
    debug("MWG POS Logger", ...args);
};

export const LoggerInfo = function (...args) {
    info("MWG POS Logger", ...args);
};

export const LoggerWarn = function (...args) {
    warn("MWG POS Logger", ...args);
};

export const LoggerError = function (...args) {
    error("MWG POS Logger", ...args);
};

export const LoggerInfoDeviceIP = async (...args) => {
    const body = {
        deviceId: DEVICE.uniqueId,
        deviceName: DEVICE.model,
        macAddress: "",
        localIP: "",
        publicIP: ""
    };
    try {
        body.macAddress = await getMacAddress();
        const connectInfo = await NetInfo.fetch();
        body.localIP = connectInfo.details.ipAddress;
        const response = await fetch(URL_FIND_IP);
        body.publicIP = await response.text();
    } catch (error) {
        console.log("LoggerInfoDeviceIP error", error);
    }
    info("MWG POS Logger", body, ...args);
};

/* linking */
export const openURL = (url) => {
    Linking.canOpenURL(url).then(supported => {
        if (supported) {
            Linking.openURL(url);
        }
    });
};

export const isMatchedInstallmentStore = (storeID) => {
    return true;
    // const storeIDListInstallment = "580,1485"
    // if (storeIDListInstallment) {
    //     return `,${storeIDListInstallment},`.includes(`,${storeID},`);
    // } else {
    //     return true
    // }
};

export const addStoreConditionsSimItel = (storeID) => {
    const storeSimItel = "411";
    if (storeSimItel) {
        return `,${storeSimItel},`.includes(`,${storeID},`);
    } else {
        return false;
    }
};


/*  */

export const resizeImage = ({ width, height, uri }) => {
    return new Promise((resolve, reject) => {
        ImageResizer.createResizedImage(
            uri,
            width,
            height,
            "JPEG",
            100,
            0
        ).then((response) => {
            resolve(response);
        }).catch((error) => {
            reject(error);
        });
    });
};


export const rotateImage = ({ width, height, uri, rotation }) => {
    return new Promise((resolve, reject) => {
        let max = 1024;
        let w = width;
        let h = height;

        if (width > height) {
            // orientation = 'landscape';
            if (w > max) {
                w = max;
                h = max * height / width;
            }
        } else if (width < height) {
            //orientation = 'portrait';
            if (h > max) {
                h = max;
                w = max * width / height;
            }
        } else { //width == height
            //orientation = 'event';
            if (w > max) {
                w = max;
                h = max * height / width;
            }
        }

        ImageResizer.createResizedImage(
            uri,
            w,
            h,
            "JPEG",
            100,
            rotation ? rotation : 0
        ).then((response) => {
            resolve(response);
        }).catch((error) => {
            reject(error);
        });
    });
};

/*  */
const regExpCharVN = /^([\u0020-\u007E \u00C0-\u00CD\u00D2-\u00DD\u00E0-\u00ED\u00F2-\u00FD\u0102-\u0103\u0110-\u0111\u0128-\u0129\u0168-\u0169\u01A0-\u01A1\u01AF-\u01B0\u1EA0-\u1EF9 \u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9])*$/u;
export const isValidateCharVN = (text) => {
    return regExpCharVN.test(text);
};

const regExpPhoneRefix = /^010|011|012|0120|0121|0122|0123|0124|0125|0126|0127|0128|0129|015|016|0161|0162|0163|0164|0165|0166|0167|0168|0169|017|018|0186|0188|0197|0198|0199|020|0208|0210|0214|0220|0225|0228|0232|0236|0238|0243|0248|025|0251|0252|0254|0255|0258|0260|0262|0263|0269|027|0270|0271|0272|0275|0277|028|0282|0283|0285|0286|0291|0296|030|031|032|033|034|0342|035|036|037|038|039|051|052|0522|053|055|0552|056|057|058|059|060|061|066|067|0673|068|069|070|071|074|075|076|077|078|079|080|081|082|083|084|085|086|087|088|089|090|091|092|093|094|095|096|097|098|099|190|9282|0292/;
export const isValidatePhonePrefix = (phone) => {
    return regExpPhoneRefix.test(phone);
};

export const isValidatePhone = (phone) => {
    const regexPhone = global.isVN ? /^[0][\d]{9,10}$/ : /^[0][\d]{8,9}$/;
    return regexPhone.test(phone);
};

export const isValidEmail = (email) => {
    return regexEmail.test(email);
};

/*  */
export const isHasDiscount = (promotionProducts) => {
    const itemDiscount = promotionProducts.find(ele => hasProperty(ele, "isDiscount"));
    return !!itemDiscount;
};

export const isDiscount = (product) => {
    return hasProperty(product, isDiscount);
};

export const handelGiftPromotion = (giftPromotion) => {
    let dataPromotion = [];
    if (hasProperty(giftPromotion, "subCategory")) {
        const { subCategory } = giftPromotion;
        if (isArray(subCategory) && subCategory.length > 0) {
            const { promotionGroups } = subCategory[0];
            if (isArray(promotionGroups)) {
                dataPromotion = [...promotionGroups];
            }
        }
    }
    return dataPromotion;
};

export const handelSalePromotion = (salePromotion) => {
    let dataPromotion = [];
    if (hasProperty(salePromotion, "subCategory")) {
        const { subCategory } = salePromotion;
        if (isArray(subCategory)) {
            dataPromotion = [...subCategory];
        }
    }
    return dataPromotion;
};

export const getAllKeyPromotion = (promotion, salePromotion) => {
    const allKeyPromotion = new Set();
    const allGroupID = new Set();
    const allPromotionID = new Set();
    promotion.forEach((groupPromotion) => {
        const {
            promotionGroupID,
            promotionProducts,
            promotionID,
            groupType
        } = groupPromotion;
        const isHasInstock = promotionProducts.some(product => product.instockQuantity > 0);
        // const isKeepGroup = (groupType != 3) || isHasInstock;
        const isKeepGroup = true;
        allGroupID.add(promotionGroupID);
        allPromotionID.add(promotionID);
        if (isKeepGroup) {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                allKeyPromotion.add(key);
            });
        }
    });
    salePromotion.forEach((subCategory) => {
        let { promotionGroups } = subCategory;
        promotionGroups.forEach((groupPromotion) => {
            let { promotionGroupID, promotionID } = groupPromotion;
            allGroupID.add(promotionGroupID);
            allPromotionID.add(promotionID);
        });
    });
    return { allKeyPromotion, allGroupID, allPromotionID };
};

export const getExcludeContent = (
    groupPromotion,
    allPromotionID,
    dataPromotion,
    dataSalePromotion,
    promotionName = "Khuyến mãi HTN:",
    salePromotionName = "Bán kèm HTN:",
) => {
    const { excludePromotionIDs } = groupPromotion;
    let contentExclude = "";
    let newExcludePromotionIDs = [];
    if (IsNonEmptyArray(excludePromotionIDs)) {
        newExcludePromotionIDs = excludePromotionIDs.filter(promotionID => allPromotionID.has(promotionID));
        if (IsNonEmptyArray(newExcludePromotionIDs)) {
            let contentExcludePromotion = "";
            let contentExcludeSalePromotion = "";
            newExcludePromotionIDs.map(excludeID => {
                dataPromotion.forEach(groupPromotion => {
                    const { promotionID, promotionGroupName } = groupPromotion;
                    if (excludeID == promotionID) {
                        contentExcludePromotion += `\n\t${promotionGroupName}`;
                    }
                });
                dataSalePromotion.forEach((subCategory) => {
                    const { promotionGroups, subCategoryName } = subCategory;
                    promotionGroups.forEach((groupPromotion) => {
                        const { promotionID, promotionGroupName } = groupPromotion;
                        if (excludeID == promotionID) {
                            contentExcludeSalePromotion += `\n\t${promotionGroupName} (${subCategoryName})`;
                        }
                    });
                });
            });
            contentExcludePromotion = contentExcludePromotion
                ? `\n${promotionName} ${contentExcludePromotion}`
                : "";
            contentExcludeSalePromotion = contentExcludeSalePromotion
                ? `\n${salePromotionName} ${contentExcludeSalePromotion}`
                : "";
            contentExclude = contentExcludePromotion + contentExcludeSalePromotion;
        }
    }
    return { contentExclude, newExcludePromotionIDs };
};

export const getDefaultKeyPromotion = (promotion) => {
    const defaultKeyPromotion = new Set();
    promotion.forEach((groupPromotion) => {
        const {
            promotionGroupID,
            promotionProducts,
            isCheckCustomer,
            isCondition,
            excludePromotionIDs,
            isViewBarcode
        } = groupPromotion;
        if (!groupPromotion.isRandomDiscount) {
            const isNonExclude = (excludePromotionIDs.length == 0);
            const isOnly = (promotionProducts.length == 1);
            const isNonCondition = !isCondition;
            const isNonCheckPhone = !isCheckCustomer;
            const isDefault = (!isViewBarcode && isNonExclude && isNonCondition && isNonCheckPhone && isOnly);
            if (isNonCheckPhone) {
                promotionProducts.forEach((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    if (isDefault) {
                        defaultKeyPromotion.add(key);
                    }
                });
            }
        }
    });
    return defaultKeyPromotion;
};

export const getAllExcludeDisabled = (promotion, salePromotion) => {
    let excludeDisable = [];
    promotion.forEach((groupPromotion) => {
        const {
            excludePromotionIDs,
            disableApply
        } = groupPromotion;
        if (disableApply) {
            if (IsNonEmptyArray(excludePromotionIDs)) {
                excludeDisable = [...excludeDisable, ...excludePromotionIDs];
            }
        }
    });
    salePromotion.forEach((subCategory) => {
        let { promotionGroups } = subCategory;
        promotionGroups.forEach((groupPromotion) => {
            const {
                excludePromotionIDs,
                disableApply
            } = groupPromotion;
            if (disableApply) {
                if (IsNonEmptyArray(excludePromotionIDs)) {
                    excludeDisable = [...excludeDisable, ...excludePromotionIDs];
                }
            }
        });
    });
    return new Set(excludeDisable);
};

export const getExcludePromotionID = (promotion, salePromotion, setKeyPromotion) => {
    let allExcludeID = [];
    promotion.forEach((groupPromotion) => {
        const {
            promotionGroupID,
            promotionProducts,
            excludePromotionIDs
        } = groupPromotion;
        const isSelected = promotionProducts.some((product, index) => {
            const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
            const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
            return setKeyPromotion.has(key);
        });
        const isHasExclude = isSelected && IsNonEmptyArray(excludePromotionIDs);
        if (isHasExclude) {
            allExcludeID = [...allExcludeID, ...excludePromotionIDs];
        }
    });
    salePromotion.forEach((subCategory) => {
        let { promotionGroups } = subCategory;
        promotionGroups.forEach((groupPromotion) => {
            const {
                promotionGroupID,
                promotionProducts,
                excludePromotionIDs
            } = groupPromotion;
            const isSelected = promotionProducts.some((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                return setKeyPromotion.has(key);
            });
            const isHasExclude = isSelected && IsNonEmptyArray(excludePromotionIDs);
            if (isHasExclude) {
                allExcludeID = [...allExcludeID, ...excludePromotionIDs];
            }
        });
    });
    return new Set(allExcludeID);
};

export const excludeKeyPromotionSelected = (
    promotion,
    salePromotion,
    setKeyPromotionSelected,
    excludePromotionIDs,
    saleProductGroupID,
    csPromotion = []
) => {
    const setExcludePromotion = new Set(excludePromotionIDs);
    const newSetKeyPromotionSelected = new Set(setKeyPromotionSelected);
    promotion.forEach((groupPromotion) => {
        const { promotionID, promotionGroupID, promotionProducts } = groupPromotion;
        if (setExcludePromotion.has(promotionID)) {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                if (Number(saleProductGroupID) == Number(promotionListGroupIDForSaleProduct)) {
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    newSetKeyPromotionSelected.delete(key);
                }
            });
        }
    });
    salePromotion.forEach((subCategory) => {
        const { promotionGroups } = subCategory;
        promotionGroups.forEach((groupPromotion) => {
            const { promotionID, promotionGroupID, promotionProducts } = groupPromotion;
            if (setExcludePromotion.has(promotionID)) {
                promotionProducts.forEach((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    if (Number(saleProductGroupID) == Number(promotionListGroupIDForSaleProduct)) {
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        newSetKeyPromotionSelected.delete(key);
                    }
                });
            }
        });
    });
    csPromotion.forEach((groupPromotion) => {
        const { promotionID, promotionGroupID, promotionProducts } = groupPromotion;
        if (setExcludePromotion.has(promotionID)) {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                if (Number(saleProductGroupID) == Number(promotionListGroupIDForSaleProduct)) {
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    newSetKeyPromotionSelected.delete(key);
                }
            });
        }
    });
    return newSetKeyPromotionSelected;
};
/*  */
export const convertNum = function (num, isCurrency = true) {
    if (!isNumber(num)) return `${num}`;
    const str = num.toFixed(4);
    let [int, dec] = str.split(".");
    const intMasked = int.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    if (dec) { while (dec[dec.length - 1] == '0') { dec = dec.slice(0, -1); } }
    const decMasked = dec ? `.${dec}` : "";
    const currency = isCurrency ? global.currency : "";
    return `${intMasked}${decMasked}${currency}`;
};

const sliceString = (data) => {
    let newData = data.toString();
    let result = [];
    if (isString(newData) && (newData != '0')) {
        let len = newData.length;
        let mod = Math.floor(len / 3);
        for (let index = mod; index >= 0; index--) {
            let begin = -(index + 1) * 3;
            let end = len - index * 3;
            let subString3 = newData.slice(begin, end);
            if (subString3) {
                result.push(subString3);
            }
        }
    }
    return result;
};

const maskString = (result) => {
    let dataFormat = "";
    if (result.length > 0) {
        dataFormat = result[0];
        for (let i = 1; i < result.length; i++) {
            dataFormat += ("," + result[i]);
        }
    }
    return dataFormat;
};

export const convertMaskString = (strNumber) => {
    return maskString(sliceString(strNumber));
};

export const removeMaskString = (maskNumber) => {
    let result = "";
    if (isString(maskNumber)) {
        result = maskNumber.replace(/[,]/g, "");
    }
    return result;
};

const isValidHex = function (color) {
    if (!color || typeof color !== 'string') return false;
    if (color.substring(0, 1) === '#') color = color.substring(1);
    switch (color.length) {
        case 3: return /^[0-9A-F]{3}$/i.test(color);
        case 6: return /^[0-9A-F]{6}$/i.test(color);
        case 8: return /^[0-9A-F]{8}$/i.test(color);
        default: return false;
    }
};

export const getColor = function (color) {
    if (isValidHex(color)) {
        if (color.substring(0, 1) === '#') {
            return color;
        } else {
            color = '#' + color;
        }
    }
    return color.toLowerCase();
};

export const getDateTimeSuggest = (suggestTime) => {
    const data = {};
    let beginDate = '';
    let endDate = '';
    const len = suggestTime.length;
    let defaultDate = '';
    if (len > 0) {
        beginDate = suggestTime[0];
        endDate = suggestTime[len - 1];
        suggestTime.forEach((ele, index) => {
            const { deliveryValue, isDefault } = ele;
            if (isString(deliveryValue)) {
                const key = deliveryValue.split("T")[0];
                if (hasProperty(data, key)) {
                    data[key].push(ele);
                }
                else {
                    data[key] = [ele];
                }
            }
            if (!defaultDate && isDefault) {
                defaultDate = ele;
            }
        });
    }
    return { data, beginDate, endDate, defaultDate };
};
/* network */
const NET_INT_CONFIG = new Set([174352384, 174354432, 174356480]);

const ip2int = (ip) => {
    if (IsNonEmptyString(ip)) {
        return ip.split('.').reduce((ipInt, octet) => {
            return (ipInt << 8) + parseInt(octet, 10);
        }, 0) >>> 0;
    }
    else {
        return 0;
    }
};

const checkNetworkSale = (network) => {
    const { ipAddress, subnet } = network;
    const ipInt = ip2int(ipAddress);
    const subInt = ip2int(subnet);
    const netInt = ipInt & subInt;
    return NET_INT_CONFIG.has(netInt);
};

export const checkWifiInternal = async () => {
    let isInternal = false;
    try {
        const connectInfo = await NetInfo.fetch();
        const { details, type } = connectInfo;
        if (type == "wifi") {
            isInternal = checkNetworkSale(details);
            if (!isInternal) {
                const response = await fetch(URL_FIND_IP);
                const ipPublic = await response.text();
                isInternal = await findStoreByIP(ipPublic);
            }
        }
    } catch (error) {
        console.log("checkWifiInternal", error);
    }
    return isInternal;
};

export const checkStorePermission = async (storeID) => {
    let isHasRight = true;
    try {
        isHasRight = await findStorePermission(storeID);
    } catch (error) {
        console.log("checkStorePermission", error);
    }
    return isHasRight;
};

/*  */
export const checkConfigTimeSale = (brandID) => {
    const { AllowTimeCreateSO } = global.config;
    const hh_mm = dateHelper.formatDateHHMM();
    if (AllowTimeCreateSO == "-1") {
        return true;
    }
    else {
        let start = "00:00";
        let finish = "23:59";
        const sub = `;${brandID}|`;
        try {
            const [item] = `;${AllowTimeCreateSO}`.split(sub).pop().split(';');
            const [begin, end] = item.split("-");
            start = (begin.length < 5) ? `0${begin}` : begin;
            finish = (end.length < 5) ? `0${end}` : end;
        } catch (error) {
            console.log('checkConfigTimeSale error', error);
        }
        return start <= hh_mm && hh_mm <= finish;
    }
};

export const checkConfigTimeSalePreOrder = (storeID) => {
    const { ALLOWTIMECREATESOBYSTORE } = global.config;
    const hh_mm = dateHelper.formatDateHHMM();
    if (ALLOWTIMECREATESOBYSTORE == "-1") {
        return true;
    }
    else {
        let start = "00:00";
        let finish = "23:59";
        try {
            const data = `${ALLOWTIMECREATESOBYSTORE}`.split(';');
            const item = data.find(ele => `,${ele}`.includes(`,${storeID}`));
            if (item) {
                const time = item.split('|');
                const [begin, end] = time.split("-");
                start = (begin.length < 5) ? `0${begin}` : begin;
                finish = (end.length < 5) ? `0${end}` : end;
            }
            else {
                return false;
            }
        } catch (error) {
            console.log('checkConfigTimeSalePreOrder error', error);
        }
        return start <= hh_mm && hh_mm <= finish;
    }
};

export const checkConfigCreateSO = (userName) => {
    const { ALLOWUSERCREATESO } = global.config;
    if (ALLOWUSERCREATESO == "-1") {
        return true;
    }
    else {
        const setKey = new Set(ALLOWUSERCREATESO.split(","));
        return setKey.has(`${userName}`);
    }
};

export const checkConfigStoreEGPromotion = (store) => {
    const { STOREPROMOTIONENGINE } = global.config;
    if (STOREPROMOTIONENGINE == "-1") {
        return true;
    }
    else {
        const setKey = new Set(STOREPROMOTIONENGINE.split(","));
        return setKey.has(`${store}`);
    }
};

export const checkConfigBrandEGPromotion = (brandID) => {
    const { BRANDPROMOTIONENGINE } = global.config;
    if (BRANDPROMOTIONENGINE == "-1") {
        return true;
    }
    else {
        const setKey = new Set(BRANDPROMOTIONENGINE.split(","));
        return setKey.has(`${brandID}`);
    }
};

export const checkConfigProvinceEGPromotion = (provinceID) => {
    const { PROVINCEPROMOTIONENGINE } = global.config;
    if (PROVINCEPROMOTIONENGINE == "-1") {
        return true;
    }
    else {
        const setKey = new Set(PROVINCEPROMOTIONENGINE.split(","));
        return setKey.has(`${provinceID}`);
    }
};

export const checkConfigAllEGPromotion = (store) => {
    const { ALLSTOREPROMOTIONENGINE } = global.config;
    if (ALLSTOREPROMOTIONENGINE == "-1") {
        return true;
    }
    else {
        const setKey = new Set(ALLSTOREPROMOTIONENGINE.split(","));
        return setKey.has(`${store}`);
    }
};

export const checkConfigStoreNCPromotion = (store) => {
    const { STOREPROMOTIONNETCORE } = global.config;
    if (STOREPROMOTIONNETCORE == "-1") {
        return true;
    }
    return STOREPROMOTIONNETCORE.split(",").includes(`${store}`);
};


export const checkConfigStorePrint = (store) => {
    return false;
    // const { SERVICESPRINTNEW_STORE } = global.config;
    // if (SERVICESPRINTNEW_STORE == "-1") {
    //     return true;
    // }
    // else {
    //     const setKey = new Set(SERVICESPRINTNEW_STORE.split(","));
    //     return !setKey.has(`${store}`);
    // }
};

export const checkConfigStorePrintTCP = (store) => {
    return PRINT_CONFIG.has(`${store}`);
};

export const checkConfigUserCheckInTopZone = (user) => {
    const { USERCHECKINTOPZONE } = global.config;
    const setKey = new Set(USERCHECKINTOPZONE.split(','));
    return setKey.has(`${user}`);
};

export const checkConfigStoreInstallmentNew = (storeID) => {
    const { STORE_BREAKEBOD_POSTRANSACTION } = global.config;
    if (STORE_BREAKEBOD_POSTRANSACTION == "-1") {
        return true;
    }
    else {
        const setKey = new Set(STORE_BREAKEBOD_POSTRANSACTION.split(","));
        return setKey.has(`${storeID}`);
    }
};

export const getAppConfigNumber = (key) => {
    const value = global.config[key];
    if (isString(value)) {
        return parseInt(value);
    }
    return -1;
};

export const checkConfigStoreInstallmentFENew = (storeID) => {
    const { STORE_POSTRANSACTION_UPGRADEAPIFE } = global.config;
    if (STORE_POSTRANSACTION_UPGRADEAPIFE == "-1") {
        return true;
    }
    else {
        const setKey = new Set(STORE_POSTRANSACTION_UPGRADEAPIFE.split(","));
        return setKey.has(`${storeID}`);
    }
};

export const checkConfigStoreInstallmentHCAndACSNew = (storeID) => {
    const { STORE_POSTRANSACTION_MODIFY } = global.config;
    if (STORE_POSTRANSACTION_MODIFY == "-1") {
        return true;
    }
    else {
        const setKey = new Set(STORE_POSTRANSACTION_MODIFY.split(","));
        return setKey.has(`${storeID}`);
    }
};

export const checkConfigStoreInstallmentMCNew = (storeID) => {
    const { STORE_POSTRANSACTION_MODIFY_MC } = global.config;
    if (STORE_POSTRANSACTION_MODIFY_MC == "-1") {
        return true;
    }
    else {
        const setKey = new Set(STORE_POSTRANSACTION_MODIFY_MC.split(","));
        return setKey.has(`${storeID}`);
    }
};

export const checkConfigStoreInsuranceLocationFlowNew = () => {
    const { INSURANCE_AIRTIMETYPE_NEWUI } = global.config;
    const getDateFromString = (dateStr) => {
        const [day, month, year] = dateStr.split('/');
        return new Date(`${year}-${month}-${day}`);
    };

    const configDate = getDateFromString(INSURANCE_AIRTIMETYPE_NEWUI);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    configDate.setHours(0, 0, 0, 0);

    return today >= configDate;
};


export const getUniqueID = (selectedItemsArray) => {
    const result = [];
    const setKeys = new Set();
    selectedItemsArray.forEach(ele => {
        if (!setKeys.has(ele.PriceInforDetailID)) {
            result.push(ele);
            setKeys.add(ele.PriceInforDetailID);
        }
    });
    return result;
};

export const addStoreConditionsSimMobi = (storeID) => {
    return true;
    // const storeSimMobi = "11185";
    // if (storeSimMobi) {
    //     return `,${storeSimMobi},`.includes(`,${storeID},`);
    // } else {
    //     return false;
    // }
};

export const splitCustomerName = (objInfoCard) => {
    let customerName = '';
    let middleName = '';
    let firstName = null;
    let lastName = null;

    if (objInfoCard.full_name && objInfoCard.full_name.length > 0) {
        customerName = objInfoCard.full_name.split(' ');
        if (customerName.length > 0) {
            firstName = customerName[customerName.length - 1];
            lastName = customerName.length > 1 ? customerName[0] : '';
            if (customerName.length > 2) {
                for (let i = 1; i < customerName.length - 1; i++) {
                    middleName += customerName[i] + ' ';
                }
                // Nếu tên đệm hơn 15 ký tự thì không gán vào họ, tên, tên đệm
                if (middleName.trim().length >= 15) {
                    firstName = '';
                    middleName = '';
                    lastName = '';
                } else {
                    middleName = middleName.trim();
                }
            }
        }
    }

    return {
        firstName,
        middleName,
        lastName
    };
};

export const bindDataHtml = (dataSaleOrder) => {
    const {
        CustomerInfo,
        DeliveryInfo,
        SaleOrderDetails,
        TotalAmount,
        TotalPaid,
        IsIncome,
        IsDelivery,
        DeliveryTypeID,
        TotalRemain,
        SaleReceiptID,
        Logo,
        Hotline,
        WarrantyPhone,
        CompanyName,
        WarrantyWebsite
    } = dataSaleOrder;
    const isAtHome = (DeliveryTypeID != 1) ? "" : "collapse";
    const isInCome = IsIncome ? "" : "collapse";
    const isNotInCome = !IsIncome ? "" : "collapse";
    const deliveryStatus = IsDelivery ? "Đã giao" : "Chưa giao";
    const productList = [];
    SaleOrderDetails.forEach((mainProduct, index) => {
        const { giftSaleOrders } = mainProduct;
        productList.push({
            "STT": `${index + 1}.`,
            "PRODUCTID": mainProduct.ProductID,
            "PRODUCTNAME": mainProduct.ProductName,
            "SALEPRICE": convertNum(mainProduct.SalePriceVAT, false),
            "QUANTITY": mainProduct.Quantity,
            "TOTALCOST": convertNum(mainProduct.SalePriceVAT * mainProduct.Quantity, false),
            "IMEI": mainProduct.IMEI,
            "HAVEIMEI": !IsNonEmptyString(mainProduct.IMEI) ? "collapse" : "",
            "INVENTORYSTATUS": "",
        });
        giftSaleOrders.forEach(giftProduct => {
            if (!hasProperty(giftProduct, 'IsPercentDiscount')) {
                productList.push({
                    "STT": "+",
                    "PRODUCTID": giftProduct.ProductID,
                    "PRODUCTNAME": giftProduct.ProductName,
                    "QUANTITY": giftProduct.Quantity,
                    "INVENTORYSTATUS": "",
                    "IMEI": giftProduct.IMEI,
                    "HAVEIMEI": !IsNonEmptyString(giftProduct.IMEI) ? "collapse" : "",
                    "SALEPRICE": 0,
                    "TOTALCOST": 0,
                });
            }
        });
    });
    const deliveryName = (DeliveryTypeID != 1) ? "Giao tại nhà" : "Giao tại siêu thị";
    return ({
        "LOGO": Logo,
        "HOTLINE": Hotline,
        "WARRANTYPHONE": WarrantyPhone,
        "COMPANYNAME": CompanyName,
        "WARRANTYWEBSITE": WarrantyWebsite,
        "DELIVERYADDRESS": DeliveryInfo.DeliveryAddress,
        "DELIVERYSTATUS": deliveryStatus,
        "ISDELIVERYHOME": isAtHome,
        "DELIVERYTYPENAME": deliveryName,
        "DELIVERYTIME": dateHelper.formatStrDateFULL(DeliveryInfo.DeliveryTime),
        "CUSTOMERNAME": CustomerInfo.CustomerName,
        "CUSTOMERPHONE": CustomerInfo.CustomerPhone,
        "ISINCOME": isInCome,
        "ISNOTINCOME": isNotInCome,
        "PRODUCTLIST": productList,
        "TOTALAMOUNT": convertNum(TotalAmount, false),
        "TOTALPAID": convertNum(TotalPaid, false),
        "REMAIN": convertNum(TotalRemain, false),
        "SAID": SaleReceiptID
    });
};

export const parseIntValue = (value) => {
    return parseInt(value) || 0;
};

export const isCheckQRCodeBank = (storeID) => {
    const STORE_BANK = "114,2043,7179,10154";
    const setKey = new Set(STORE_BANK.split(","));
    return setKey.has(`${storeID}`);
}

export const isCheckQRCodeCollection = (storeID) => {
    return true;
}

export const formatDateFromString = (dateStr) => {
    if (!/^\d{8}$/.test(dateStr)) return 'Invalid format';

    const day = dateStr.slice(0, 2);
    const month = dateStr.slice(2, 4);
    const year = dateStr.slice(4, 8);

    return `${year}-${month}-${day}`;
}


export const getTemplateOfflineSR = (data) => {
    return `<html>
  <head></head>
  <body>
    <meta charset="utf-8" />
    <style type="text/css">
      table {
        font-family: Arial;
        font-size: 35px;
        width: 750px;
      }
      .bold {
        font-weight: bold;
      }

      table td {
        line-height: 1.2em;
      }

      .text-center {
        text-align: center;
      }

      .text-left {
        text-align: left;
      }

      .text-right {
        text-align: right;
      }
      .text-vertical {
        vertical-align: middle;
      }
      .text-vertical-top {
        vertical-align: top;
      }
      .logo_tgdd {
        content: url(https://cdn.tgdd.vn/erp/SIMLogos/Logo_TGDD.jpg);
        width: 10cm;
      }
      .logo_dmx {
        content: url(https://cdn.tgdd.vn/erp/SIMLogos/Logo_DMX.jpg);
        width: 10cm;
        height: 2.2cm;
      }
      .logo_bigphone {
        content: url(https://cdn.tgdd.vn/erp/SIMLogos/logo_BIGPHONE.jpg);
        width: 10cm;
        height: 2.2cm;
      }
      .logo_dtsr {
        content: url(https://cdn.tgdd.vn/erp/SIMLogos/Logo_DTSR.jpg);
        width: 10cm;
        height: 2.2cm;
      }
      .logo_bhx {
        content: url(https://cdn.tgdd.vn/erp/SIMLogos/logo_BHX.jpg);
        width: 10cm;
        height: 2.2cm;
      }
      .logo_topzone {
        content: url(https://cdn.tgdd.vn/erp/MWGLogos/LogoTopZone.jpg);
        width: 10cm;
        height: 2.2cm;
      }

      .logo_bluesport {
        content: url(https://cdn.tgdd.vn/erp/MWGLogos/avasport_logo.png);
        width: 10cm;
        height: 2.2cm;
      }

      .logo_bluekids {
        content: url(https://cdn.tgdd.vn/erp/MWGLogos/avakids_logo.png);
        width: 10cm;
        height: 2.2cm;
      }

      .logo_bluefashion {
        content: url(https://cdn.tgdd.vn/erp/MWGLogos/avafashion_logo.jpg);
        width: 10cm;
        height: 2.2cm;
      }

      .logo_ankhang {
        content: url(https://cdn.tgdd.vn/erp/MWGLogos/ak_logo.jpg);
        width: 10cm;
        height: 2.2cm;
      }

      .collapse {
        display: none;
      }
    </style>
    <table>
      <tbody>
        <tr>
          <td colspan="3" class="text-center">
            <!-- "LOGO": "logo_tgdd"  -->
            <!-- LOGO: <img class="logo_tgdd" />  -->
            <img class="${data.LOGO}" />
          </td>
        </tr>
        <tr>
          <td
            style="
              padding: 0;
              margin: 0;
              font-size: 39px;
              font-weight: bold;
              text-align: center;
            "
            colspan="3"
          >
            PHIẾU THU KIÊM<br />PHIẾU GIAO HÀNG
          </td>
        </tr>
        <tr>
          <td colspan="3"><hr style="border: 1px solid black;" /></td>
        </tr>
        <tr>
          <td colspan="3">
            <table>
              <tbody>
                <tr class="text-vertical-top">
                  <td style="width: 210px">Khách hàng:</td>
                  <td colspan="2">${data.CUSTOMERNAME}</td>
                </tr>
                <tr class="text-vertical-top">
                  <td>Điện thoại:</td>
                  <td colspan="2">${data.CUSTOMERPHONE}</td>
                </tr>
                <!-- Thông tin bổ sung khi hình thức giao "Giao tại nhà" -->
                <!-- Giao tại nhà: "ISDELIVERYHOME": "" -->
                <!-- Không giao tại nhà: "ISDELIVERYHOME": "collapse" -->
                <!-- <tr class="collapse"> -->
                <tr class="${data.ISDELIVERYHOME} text-vertical-top">
                  <td>Địa chỉ giao:</td>
                  <td colspan="2">${data.DELIVERYADDRESS}</td>
                </tr>
                <tr class="${data.ISDELIVERYHOME} text-vertical-top">
                  <td>Giờ giao:</td>
                  <td colspan="2">${data.DELIVERYTIME}</td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3"><hr style="border-top: 3px dashed black;" /></td>
        </tr>
        <tr>
          <td style="padding: 10px 0px; font-size: 36px; text-align: left; width: 300px">
            <strong>Giá bán</strong>
          </td>
          <td style="padding: 10px 0px; font-size: 36px; text-align: center">
            <strong>SL</strong>
          </td>
          <td style="padding: 10px 0px; font-size: 36px; text-align: right">
            <strong>Thành tiền</strong>
          </td>
        </tr>
        ${data.PRODUCTLIST.map((ele, index) => `<multirows class="OutputVoucherProduct">
          <!-- STT -->
          <!-- Sản phẩm chính: "STT": "1." -->
          <!-- Sản phẩm tặng: "STT": "+ " -->
          <tr class="productname">
            <td colspan="3">${index + 1} ${ele.PRODUCTNAME} - ${ele.INVENTORYSTATUS}</td>
          </tr>
          <tr class="productname ${ele.HAVEIMEI}">
            <td colspan="3">
              <span class="cls_001 bold">IMEI:</span
              ><span class="cls_001"> ${ele.IMEI}</span>
            </td>
          </tr>
          <tr class="productdetail">
            <td style="padding: 5px 0px; text-align: left">${ele.SALEPRICE}</td>
            <td style="padding: 5px 0px; text-align: right">
              <span style="text-decoration: line-through"></span>
              <span>${ele.QUANTITY}</span>
            </td>
            <td style="text-align: right">${ele.TOTALCOST}</td>
          </tr>
        </multirows>`)}
        <tr>
          <td colspan="3"><hr style="border-top: 3px dashed black;" /></td>
        </tr>
        <tr>
          <td style="text-align: right; padding: 5px 0px" colspan="2">
            Tổng tiền:
          </td>
          <td style="text-align: right">${data.TOTALAMOUNT}</td>
        </tr>
        <!-- Đã thu tiền: "ISINCOME": "" -->
        <!-- Chưa thu tiền: "ISINCOME": "collapse" -->
        <tr class="${data.ISINCOME}">
          <td style="text-align: right; padding: 5px 0px" colspan="2">
            Đã thanh toán:
          </td>
          <td style="text-align: right">${data.TOTALPAID}</td>
        </tr>
        <!-- Chưa thu tiền: "ISNOTINCOME": "" -->
        <!-- Đã thu tiền: "ISNOTINCOME": "collapse" -->
        <tr class="${data.ISNOTINCOME}">
          <td style="text-align: right" colspan="2">
            <strong style="font-size: 39px">Phải thanh toán:</strong> <br />
          </td>
          <td style="font-size: 39px; text-align: right">
            <strong>${data.REMAIN}</strong>
          </td>
        </tr>
        <tr class="${data.ISINCOME}">
          <td style="text-align: right" colspan="2">
            <strong style="font-size: 39px">Còn lại phải thu:</strong> <br />
          </td>
          <td style="font-size: 39px; text-align: right">
            <strong>${data.REMAIN}</strong>
          </td>
        </tr>
        <tr>
          <td style="text-align: right; padding-top: 10px" colspan="3">
            <strong style="font-size: 39px">${data.DELIVERYTYPENAME}</strong> <br />
          </td>
        </tr>
        <!-- Trạng thái giao của đơn hàng -->
        <!-- Chưa giao: "DELIVERYSTATUS": "Chưa Giao" -->
        <!-- Đã giao: "DELIVERYSTATUS": "Đã Giao" -->
        <tr>
          <td style="text-align: right" colspan="3">
            <strong style="font-size: 39px">Đơn hàng ${data.DELIVERYSTATUS}</strong> <br />
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <table>
              <tbody>
                <tr class="text-center">
                  <td style="padding-top: 20px; vertical-align: top">
                    <span class="cls_001 bold">Khách hàng</span>
                  </td>
                  <td style="padding-top: 20px; vertical-align: top">
                    <span class="cls_001 bold">Nhân viên</span>
                  </td>
                </tr>
                <tr class="text-center">
                  <td style="padding-top: 10px; vertical-align: top">
                    <span class="cls_001">Ký tên và ghi rõ<br/> họ tên</span>
                  </td>
                  <td style="padding-top: 10px; vertical-align: top; padding-bottom: 250px;">
                    <span class="cls_001"
                      >Ký tên và ghi rõ họ tên<br />Xác nhận đóng mộc</span
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3"><hr style="border: 1px solid black;" /></td>
        </tr>
        <tr>
          <td style="text-align: center;" colspan="3">
            <span style="font-size: 30px; font-style: italic;">Phiếu mua hàng mà quý khách được tặng sẽ được TGDĐ liên hệ và giao trong thời gian sớm nhất</span> <br/>
          </td>
        </tr>
        <tr>
          <td colspan="3"><hr style="border: 1px solid black;" /></td>
        </tr>
        <tr>
          <tr class="text-center">
            <td colspan="3" style="padding-top: 10px">
              <span class="cls_001 bold"
                >Tổng đài góp ý/ khiếu nại: ${data.HOTLINE}</span
              >
            </td>
          </tr>
          <tr class="text-center">
            <td colspan="3" style="padding: 5px 0">
              <span class="cls_001 bold">Tổng đài bảo hành: ${data.WARRANTYPHONE}</span>
            </td>
          </tr>
        </tr>
        <tr>
          <td colspan="3"><hr style="border: 1px solid black;" /></td>
        </tr>
        <tr>
          <td colspan="3" style="padding-top: 10px">
            <span class="cls_001 bold">Phiếu bảo hành sản phẩm</span>
          </td>
        </tr>
        <tr>
          <td colspan="3" style="padding: 5px 0px">
            <span class="cls_001"
              >Sản phẩm được bảo hành theo chính hãng tại tất cả các trung tâm
              bảo hành của hãng hoặc tại cửa hàng ${data.COMPANYNAME}.</span
            >
          </td>
        </tr>
        <tr>
          <td colspan="3" style="padding: 5px 0px">
            <span class="cls_001"
              >Chính sách bảo hành của hãng, địa chỉ của các trung tâm bảo
              hành quý khách vui lòng liên hệ tại SĐT: ${data.WARRANTYPHONE}, tại các cửa
              hàng của ${data.COMPANYNAME}, hoặc tại website:</span
            >
          </td>
        </tr>
        <tr>
          <td colspan="3" class="cls_001" style="padding: 5px 0px">
            <a style="text-decoration: underline"
              >${data.WARRANTYWEBSITE}</a
            >
          </td>
        </tr>
        <tr class="text-center">
          <td colspan="3" style="padding-top: 10px">
            <span class="cls_001 bold"
              >Xin cảm ơn quý khách<br />Hẹn gặp lại</span
            >
          </td>
        </tr>
        <tr class="text-center">
          <td colspan="3" style="padding-top: 10px">
            <span class="cls_001">Mã phiếu bán hàng: ${data.SAID}</span>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>`;
};

export const checkConfigMakePrice = (userName) => {
    const { LIST_USER_ACCEPT_PRICE_POS } = global.config;
    if (LIST_USER_ACCEPT_PRICE_POS == "-1") {
        return true;
    }
    else {
        const setKey = new Set(LIST_USER_ACCEPT_PRICE_POS.split(","));
        return setKey.has(`${userName}`);
    }
};

export const checkConfigIPArea = (defaultStoreID) => {
    const ALLOWPOS4G = global?.config?.ALLOWPOS4G;
    if (!ALLOWPOS4G) {
        return false;
    } else {
        const setKey = new Set(ALLOWPOS4G.split(","));
        return setKey.has(`${defaultStoreID}`);
    }
};

/*  */

//làm tròn số thập phân
export const take_decimal_number = (num, n = 0) => {
    //num : số cần xử lý
    //n: số chữ số sau dấu phẩy cần lấy
    let base = 10 ** n;
    let result = Math.round(num * base) / base;
    return result;
};
/*  */

export const formatStringByParams = function (str, ...params) {
    var formatted = str;
    for (var arg in params) {
        formatted = formatted.replace("{" + arg + "}", params[arg]);
    }
    return formatted;
};

export const groupArrayThreeLayers = (myArr, firstValue, firstTitle, secondValue, secondTitle) => {
    let result = [];
    myArr.forEach(element => {
        const firstLayerIndex = result.findIndex(ele => ele[firstValue] == element[firstValue]);
        if (firstLayerIndex == -1) {
            result.push({
                [firstValue]: element[firstValue],
                [firstTitle]: element[firstTitle],
                firstLayerData: [{
                    [secondValue]: element[secondValue],
                    [secondTitle]: element[secondTitle],
                    secondLayerData: [element]
                }]
            });
        } else {
            const secondLayerIndex = result[firstLayerIndex].firstLayerData.findIndex(ele => ele[secondValue] == element[secondValue]);
            if (secondLayerIndex == -1) {
                result[firstLayerIndex].firstLayerData.push({
                    [secondValue]: element[secondValue],
                    [secondTitle]: element[secondTitle],
                    secondLayerData: [element]
                });
            } else {
                result[firstLayerIndex].firstLayerData[secondLayerIndex].secondLayerData.push(element);
            }
        }
    });
    return result;
};

export const groupArrayThreeLayersMultiField = (myArr, firstValue, firstTitle, secondValue, secondTitle, secondValueExtra, secondTitleExtra, secondIdExtra) => {
    let result = [];
    myArr.forEach(element => {
        const firstLayerIndex = result.findIndex(ele => ele[firstValue] == element[firstValue]);
        if (firstLayerIndex == -1) {
            result.push({
                [firstValue]: element[firstValue],
                [firstTitle]: element[firstTitle],
                firstLayerData: [{
                    [secondValue]: element[secondValue],
                    [secondTitle]: element[secondTitle],
                    [secondValueExtra]: element[secondValueExtra],
                    [secondTitleExtra]: element[secondTitleExtra],
                    [secondIdExtra]: element[secondIdExtra],
                    secondLayerData: [element]
                }]
            });
        } else {
            const secondLayerIndex = result[firstLayerIndex].firstLayerData.findIndex(ele =>
                ele[secondValue] == element[secondValue] && ele[secondValueExtra] == element[secondValueExtra]
            );
            if (secondLayerIndex == -1) {
                result[firstLayerIndex].firstLayerData.push({
                    [secondValue]: element[secondValue],
                    [secondTitle]: element[secondTitle],
                    [secondValueExtra]: element[secondValueExtra],
                    [secondTitleExtra]: element[secondTitleExtra],
                    [secondIdExtra]: element[secondIdExtra],
                    secondLayerData: [element]
                });
            } else {
                result[firstLayerIndex].firstLayerData[secondLayerIndex].secondLayerData.push(element);
            }
        }
    });
    return result;
};

export const groupArrayTwoLayers = (myArr, firstValue, firstTitle) => {
    let result = [];
    myArr.forEach(element => {
        const firstLayerIndex = result.findIndex(ele => ele[firstValue] == element[firstValue]);
        if (firstLayerIndex == -1) {
            result.push({
                [firstValue]: element[firstValue],
                [firstTitle]: element[firstTitle],
                firstLayerData: [element]
            });
        } else {
            result[firstLayerIndex].firstLayerData.push(element);
        }
    });
    return result;
};

export const uuidv4 = () => uuid.v4();

export const isAppInstalled = async (scheme) => {
    let result = false;
    try {
        result = await Linking.canOpenURL(`${scheme}://`);
    } catch (err) {
        console.log('isAppInstalled', err);
    }
    return result;
};
export const hexToRgbA = (hex, alpha = 1) => {
    let c;
    if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
        c = hex.substring(1).split('');
        if (c.length === 3) {
            c = [c[0], c[0], c[1], c[1], c[2], c[2]];
        }
        c = `0x${c.join('')}`;
        return `rgba(${[(c >> 16) & 255, (c >> 8) & 255, c & 255].join(
            ','
        )},${alpha})`;
    }
    return '#000';
};

const addDays = (date, days) => {
    const copy = new Date(date);
    copy.setDate(copy.getDate() + days);
    return copy;
};

export const getLastDate = (dates) => {
    //nếu lớn hơn 30 ngày thì endDate = startDate +30
    const { startDate, endDate } = dates;
    const date1 = new Date(startDate);
    const date2 = new Date(endDate);
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    const diffDate = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDate > 30) {
        return addDays(startDate, 30);
    } else {
        return new Date(endDate);
    }
};

export const getObjectArrLock = (objectArr, products, applyQuantity = 1) => {
    if (IsNonEmptyArray(products) && isArray(objectArr)) {
        products.forEach(product => {
            if (!product?.cus_SaleOrderDetailLockTokenBO && product?.IsCallLockInstock) {
                objectArr.push({
                    "ttl": 300,
                    "businessType": "ALLOCATED_TO_SALE",
                    "productId": product.ProductID,
                    "storeId": product.OutputStoreID,
                    "inventoryStatusId": product.InventoryStatusID,
                    "quantity": product.Quantity * applyQuantity,
                    "saleorderdetailID": product.SaleOrderDetailID
                });
            }
        });
    }
};

export const setObjectArrInstock = (objectArr, products) => {
    if (IsNonEmptyArray(products) && isArray(objectArr)) {
        products.forEach(product => {
            if (!product?.cus_SaleOrderDetailLockTokenBO && product?.IsCallLockInstock) {
                product.cus_SaleOrderDetailLockTokenBO = objectArr.find(item => item.SaleOrderDetailID == product.SaleOrderDetailID);
            }
        });
    }
};

export const getObjectArrUnlock = (objectArr, products) => {
    if (IsNonEmptyArray(products) && isArray(objectArr)) {
        products.forEach(product => {
            if (product?.cus_SaleOrderDetailLockTokenBO?.GUIID && product?.IsCallLockInstock) {
                objectArr.push({ "guiId": product?.cus_SaleOrderDetailLockTokenBO?.GUIID });
            }
        });
    }
};

export const getObjectArrRelock = (objectArr, products) => {
    if (IsNonEmptyArray(products) && isArray(objectArr)) {
        products.forEach(product => {
            if (product?.cus_SaleOrderDetailLockTokenBO?.GUIID && product?.IsCallLockInstock) {
                const expiredTime = product?.cus_SaleOrderDetailLockTokenBO?.ExpiringTime;
                const currentTime = new Date().toISOString();
                if (expiredTime < currentTime) {
                    objectArr.push({
                        "ttl": 300,
                        "guiId": product?.cus_SaleOrderDetailLockTokenBO?.GUIID,
                        "saleorderdetailID": product.SaleOrderDetailID
                    });
                }
            }
        });
    }
};
export const checkChangeValueOfPrototype = (objectValue, oldObjectValue) => {
    let isChange = false;
    for (const property in objectValue) {
        if (objectValue[property] !== oldObjectValue[property]) return isChange = true;
    }
    return isChange;
};

export const getProgramPreByConfig = () => {
    const { TEASING_PRE } = global.config;
    return TEASING_PRE;
};


export const configUserAndStorePre = (storeID, userName) => {
    return true;
    const { STORE_USER_RECEIVE_PRE_INFORMATION } = global.config;

    // Destructure and split the configUser and configStore values
    const [configUser, configStore] = STORE_USER_RECEIVE_PRE_INFORMATION.split('|');

    // Check if the storeID and userName are allowed
    const isAllowStore = configStore == "-1" || configStore?.includes(storeID.toString());
    const isAllowUser = configUser == "-1" || configUser?.includes(userName.toString());

    return isAllowStore && isAllowUser;
};

export const createFormData = ({ uri, type = 'image/jpg', name = 'image_test', path = INIT, isGenDate = "true", isGenName = "false" }) => {
    const formData = new FormData();
    formData.append('file', { uri, type, name });
    formData.append('path', path);
    formData.append('isGenDate', isGenDate);
    formData.append('isGenName', isGenName);
    return formData;
};


export const checkConfigStoreBHX = (companyID) => {
    return companyID == 2;
};

export const configEventPre = (storeID) => {
    const { STOREDDAY_PREORDER } = global.config;
    const isAllowStore = STOREDDAY_PREORDER == "-1" || STOREDDAY_PREORDER?.includes(storeID.toString());
    return isAllowStore;
};

export const configUserAdjustFee = (userName) => {
    const { USERADJUSTFEE } = global.config;
    const isAllowUser = USERADJUSTFEE == "-1" || USERADJUSTFEE?.includes(userName.toString());
    return isAllowUser;
};

export const getTypeImage = (identificationType, indexSelected) => {
    switch (identificationType) {
        case ID_CARD_TYPE.CMND: // Chứng minh nhân dân
            switch (indexSelected) {
                case 0:
                    return ID_CARD_SIDE.CMND.FRONT;
                case 1:
                    return ID_CARD_SIDE.CMND.BACK;
                default:
                    return ID_CARD_SIDE.CMND.UNKNOWN;
            }
        case ID_CARD_TYPE.CCCD:
            switch (indexSelected) {
                case 0:
                    return ID_CARD_SIDE.CCCD.FRONT;
                case 1:
                    return ID_CARD_SIDE.CCCD.BACK;
                default:
                    return ID_CARD_SIDE.CCCD.UNKNOWN;
            }
        case ID_CARD_TYPE.CCCD_NEW:
            switch (indexSelected) {
                case 0:
                    return ID_CARD_SIDE.CCCD_NEW.FRONT;
                case 1:
                    return ID_CARD_SIDE.CCCD_NEW.BACK;
                default:
                    return ID_CARD_SIDE.CCCD_NEW.UNKNOWN;
            }
        default:
            return ID_CARD_TYPE.UNKNOWN;
    }
};

export const configIntervalPayment = () => {
    const { ISINTERVALMONEYBANKSRRANSFER } = global.config;
    return ISINTERVALMONEYBANKSRRANSFER;
};

export const configScreenProtectorDiscount = (brandID) => {
    const { SCREENPROTECTOR_DISCOUNT } = global.config;
    const result = Object.fromEntries(
        SCREENPROTECTOR_DISCOUNT.split(";").map(part => {
            const [key, value] = part.split(":");
            return key && value ? [key, value] : null;
        }).filter(Boolean)
    );
    return result[brandID] ?? "";
};

export const getImageURL = (filePath) =>
    filePath.includes("cdnv2") ? filePath : THUMBNAIL_DESKTOP + filePath;

const validPartners = new Set([
    String(PARTNER_ID.MOMO),
    String(PARTNER_ID.HOME_PAY_LATER),
    String(PARTNER_ID.KREDIVO),
    String(PARTNER_ID.CAKE),
    String(PARTNER_ID.QTV),
    String(PARTNER_ID.TPBanhEVO),
]);

export const isNewFollowPartner = (partnerInstallmentID) => {
    return validPartners.has(String(partnerInstallmentID));
};

export const isWithinSaleHours = ({ brandID, userName, storeID }) => {
    return (
        checkConfigCreateSO(userName) ||
        checkConfigTimeSale(brandID) ||
        checkConfigTimeSalePreOrder(storeID)
    );
};

export const configStoreCreatePrice = (storeID) => {
    const { STOREIDLIST_APPLYCREATEPRICEBYPMH } = global.config;
    return (
        STOREIDLIST_APPLYCREATEPRICEBYPMH == "-1" ||
        STOREIDLIST_APPLYCREATEPRICEBYPMH?.includes(storeID.toString())
    );
};

export const isNewPricingCampaign = (SaleOrderDetails, storeID) => {
    return SaleOrderDetails.some(({ AdjustPrice, saleSaleOrders }) =>
        AdjustPrice != 0 ||
        saleSaleOrders?.some(({ AdjustPrice: saleAdjustPrice }) => saleAdjustPrice != 0)
    ) && configStoreCreatePrice(storeID);
};

export const getTotalAmountAdjustPrice = (SaleOrderDetails) => {
    return SaleOrderDetails?.reduce((total, { saleSaleOrders, AdjustPrice }) => {
        total += Math.abs(AdjustPrice || 0);
        total += saleSaleOrders.reduce((sum, { AdjustPrice: saleAdjustPrice }) => {
            return sum + Math.abs(saleAdjustPrice || 0);
        }, 0);
        return total;
    }, 0);
};

export const handleThreePrice = (props, saleProgramID) => {
    const {
        dataShoppingCart,
        userInfo: { storeID, languageID, moduleID },
        saleScenarioTypeID,
        actionDetail
    } = props;

    const newSaleOrderDetails = [...dataShoppingCart.SaleOrderDetails];
    const allPromises = newSaleOrderDetails
        .filter(product => !IsEmptyObject(product.PricePolicyApplyBO))
        .map(product => {
            const bodyThreePrice = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                saleScenarioTypeID,
                productProlicyBO: {
                    productID: product.ProductID,
                    imei: product.IMEI,
                    inventoryStatusID: product.InventoryStatusID
                },
                outputStoreID: storeID,
                dateTime: null,
                saleProgramID: saleProgramID ?? 0,
                deliveryTypeID: product.DeliveryInfoRequest?.DeliveryTypeID ?? 1,
                customerInfoProlicyBO: {
                    customerPhone: ""
                },
                salePricePolicyID: product.PricePolicyApplyBO?.SalePricePolicyID || 0,
                isEdit: true
            };
            return actionDetail.getPackageService(bodyThreePrice);
        });

    return { newSaleOrderDetails, allPromises };
};

export const roundNumber = (number, numberOfFractionDigits = 2) => Math.round((number + Number.EPSILON) * Math.pow(10, numberOfFractionDigits)) / Math.pow(10, numberOfFractionDigits);

export const validateOtpSend = (type) => {
    const { BYPASSOTPSCREEN } = global.config;
    return (BYPASSOTPSCREEN == "-1" && type != "INSTALLMENT")
};
export const getGuideOTPBackup = (type) => {
    const content = {
        INSTALLMENT: "Khách hàng đồng ý ủy quyền cho TGDĐ thu thập và chuyển giao thông tin cho công ty tài chính để làm hồ sơ trả góp.",
        BATTERY: "Khách hàng đồng ý thay pin Đồng Hồ.",
        STICKER: "Khách hàng đồng ý dán lại Màn Hình.",
        CONTRACT: "Khách hàng đồng ý hủy hợp đồng trả góp.",
        SCREENPROTECTOR: "Đồng ý hoàn tất tạo đơn hàng miếng dán màn hình",
        LATTER: "Khách hàng đồng ý cung cấp thông tin cá nhân cho TGDĐ thu thập, sử dụng và chuyển giao cho các đối tác.",
        ADJUSTPRICEGIFTVOUCHER: "Đồng ý hoàn tất thủ tục chiến giá.",
    }
    return content[type] || ""
}

export const configStoreApplyMapping = (storeID) => {
    const { POSSUGGESTEDDELIVERYADDRESS } = global.config;
    return (
        POSSUGGESTEDDELIVERYADDRESS == "-1" ||
        POSSUGGESTEDDELIVERYADDRESS?.includes(storeID.toString())
    );
};

export const configCouponUser = (userName) => {
    const { COUPON_CONFIG_USER } = global.config;
    return (
        COUPON_CONFIG_USER == "-1" ||
        COUPON_CONFIG_USER?.includes(userName.toString())
    );
};

export const handleCheckSelectPromotion = ({
    promotionGroup,
    setKeyPromotionSelected,
    promotion,
    isSelected,
    promotionDefaults = []
}) => {
    if (promotionDefaults?.length > 0) {
        const key = `${promotionGroup.promotionGroupID}${promotion.productID}${promotion.inventoryStatusID}${promotion.promotionListGroupIDForSaleProduct}`;
        //  Lấy danh sách khuyến mãi mặc định từ `promotionDefaults`
        const defaultSelectedKeys = new Set(promotionDefaults?.map(
            (p) => `${p.PromotionListGroupID}null00`
        ));
        // Tạo Set chứa tất cả promotionGroupID của khuyến mãi mặc định
        const selectedPromotionIDs = new Set(promotionDefaults?.map(p => p.PromotionID));

        // Kiểm tra xem promotionGroup có loại trừ khuyến mãi mặc định không
        const excludedPromotions = promotionGroup.excludePromotionIDs?.filter(id => selectedPromotionIDs.has(id));

        if (excludedPromotions.length > 0) {
            Alert.alert("Thông Báo", `Khuyến mãi này sẽ loại trừ khuyến mãi mặc định có ID: ${excludedPromotions.join(", ")}`);
            return false;
        }

        const isPromotionProduct = promotionGroup.promotionProducts?.some((promo) => promo.promotionListGroupID === promotionGroup.promotionGroupID)
        const defaultPromotions = promotionGroup.promotionProducts?.filter((promo) => setKeyPromotionSelected.has(
            `${promotionGroup.promotionGroupID}${promo.productID}${promo.inventoryStatusID}${promo.promotionListGroupIDForSaleProduct}`
        ));

        // Nếu có khuyến mãi mặc định mà chọn cái khác => Báo lỗi
        const isDefaultSelected = defaultPromotions.length > 0;
        const isDifferentPromotion = !setKeyPromotionSelected.has(key);

        if (isPromotionProduct && isDefaultSelected && isDifferentPromotion) {
            Alert.alert("Thông Báo", "Đã có khuyến mãi mặc định chọn.");
            return false;
        }

        if (isSelected && defaultSelectedKeys.has(key)) {
            Alert.alert("Thông Báo", "Bạn không được điều chỉnh khuyến mãi giảm giá khi chọn chính sách giá Siêu tiết kiệm. Vui lòng chọn lại chính sách giá");
            return false;
        }
    }
    // Nếu không có xung đột, tiếp tục xử lý chọn khuyến mãi
    return true
};

export const checkUnselectedPromotionDelivery = (PRODUCTDELIVERYPROMOTION, promotionProducts, setKeyPromotionSelected, packagePrice, deliveryType, isShipPartner = false) => {

    if ((packagePrice?.Ranking === 1 || packagePrice?.Ranking > 2) && (deliveryType === 1 || deliveryType === 2) && !isShipPartner) {
        return `Đơn hàng được tạo với chính sách giá ${packagePrice.SalePricePolicyName}, không được chọn hình thức Giao tại Siêu thị hoặc Siêu thị đi giao. Vui lòng xem lại triển khai`;
    }
    if (packagePrice?.Ranking <= 2) {
        return "";
    }

    const promoProductIDs = new Set(PRODUCTDELIVERYPROMOTION.split(','));
    const selectedProductIDs = new Set(); // Lưu các productID đã được chọn ít nhất 1 lần

    if (promotionProducts?.length > 0) {
        for (const promoGroup of promotionProducts) {
            for (const promo of promoGroup?.promotionProducts || []) {
                if (!promoProductIDs.has(promo.productID)) continue; // Bỏ qua nếu productID không thuộc danh sách khuyến mãi

                const promoKey = `${promoGroup.promotionGroupID}${promo.productID}${promo.inventoryStatusID}${promo.promotionListGroupIDForSaleProduct}`;

                if (setKeyPromotionSelected.has(promoKey)) {
                    selectedProductIDs.add(promo.productID); // Đánh dấu productID này đã có ít nhất một lựa chọn
                }
            }
        }

        // Kiểm tra lại nếu còn productID nào chưa có lựa chọn hợp lệ
        for (const promoGroup of promotionProducts) {
            for (const promo of promoGroup?.promotionProducts || []) {
                if (!promoProductIDs.has(promo.productID) || selectedProductIDs.has(promo.productID)) continue; // Bỏ qua nếu productID đã có lựa chọn

                const promoKey = `${promoGroup.promotionGroupID}${promo.productID}${promo.inventoryStatusID}${promo.promotionListGroupIDForSaleProduct}`;

                if (!setKeyPromotionSelected.has(promoKey)) {
                    return `Đơn hàng được tạo với chính sách giá ${packagePrice.SalePricePolicyName}, không được bỏ chọn khuyến mãi Miễn phí công lắp đặt. Vui lòng xem lại triển khai`;
                }
            }
        }
    }

    return "";
};

export const configStoreApplyPackage = (storeID) => {
    const { STOREPRICEPOLICY } = global.config;
    return (
        STOREPRICEPOLICY == "-1" ||
        STOREPRICEPOLICY?.includes(storeID.toString())
    );
};

export const configPromotion1975 = (promotionID) => {
    const { PROMO1975_ACTIVECAMPAIGNS } = global.config;
    return (
        PROMO1975_ACTIVECAMPAIGNS == "-1" ||
        PROMO1975_ACTIVECAMPAIGNS?.includes(promotionID.toString())
    );
};


export const checkStoreDeliveryPakageService = (storeID) => {
    const { POLICYFEE_PRODUCT_STORE } = global.config;
    if (POLICYFEE_PRODUCT_STORE == "-1") return true;
    return `,${POLICYFEE_PRODUCT_STORE},`.includes(`,${storeID.toString()},`);
}
export const detectProfileChanges = ({
    rawProfile,
    inputProfile,
    typeProfile,
}) => {
    if (!inputProfile || !typeProfile) return null;

    const defaultCustomerInfo = {
        customerName: "",
        cardCustomerId: null,
        gender: null,
        profileId: 0,
        type: 1,
        versionCode: "",
        phoneNumber: "",
        isModify: 0,
        isSigned: 0,
        signatureId: 0,
        soProfileId: null,
        relationshipTypeId: 0,
        relationshipId: 0
    };

    const defaultCompanyInfo = {
        companyId: 0,
        companyName: "",
        companyPhone: null,
        address: "",
        email: null,
        taxNo: "",
        profileId: 0,
        type: 5,
        versionCode: "",
        phoneNumber: null,
        isModify: 0,
        isSigned: 0,
        signatureId: 0,
        soProfileId: null,
        relationshipTypeId: 0,
        relationshipId: 0
    };


    const oldProfile = { ...(rawProfile?.[typeProfile]?.[0] || {}) };
    const newProfile = { ...inputProfile };

    let isChanged = false;

    switch (typeProfile) {
        case TYPE_PROFILE.CUSTOMER: {
            const oldCustomer = {
                customerName: oldProfile.customerName,
                phoneNumber: oldProfile.phoneNumber,
                gender: oldProfile.gender,
                cardCustomerId: oldProfile.cardCustomerId || "",
            };
            const newCustomer = {
                customerName: newProfile.customerName,
                phoneNumber: newProfile.customerPhone,
                gender: newProfile.gender,
                cardCustomerId: newProfile.cardCustomerId || "",
            };

            isChanged = checkChangeValueOfPrototype(newCustomer, oldCustomer);

            if (isChanged || !oldProfile.isSigned) {
                const hasPhoneChanged = oldCustomer.phoneNumber !== newCustomer.phoneNumber;

                return {
                    ...defaultCustomerInfo,
                    ...oldProfile,
                    ...newCustomer,
                    isModify: 1,
                    profileId: hasPhoneChanged ? 0 : oldProfile.profileId,
                    versionCode: hasPhoneChanged ? "" : oldProfile.versionCode,
                };
            }
            break;
        }

        case TYPE_PROFILE.COMPANY: {
            const oldCompany = {
                companyName: oldProfile.companyName,
                address: oldProfile.address,
                taxNo: oldProfile.taxNo,
            };
            const newCompany = {
                companyName: newProfile.companyName,
                address: newProfile.address,
                taxNo: newProfile.taxNo,
            };

            isChanged = checkChangeValueOfPrototype(newCompany, oldCompany);

            if (isChanged) {
                return {
                    ...defaultCompanyInfo,
                    ...oldProfile,
                    ...newCompany,
                    isModify: 1,
                };
            }
            break;
        }

        default:
            break;
    }

    return null;
};


const parseDate = (dateStr) => {
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
}

export const configApplyLocation = () => {
    const { DATECHANGEUNIT } = global.config;
    const configDate = parseDate(DATECHANGEUNIT);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today.getTime() < configDate.getTime();

}


