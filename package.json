{"name": "eagle", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@craftzdog/pouchdb-core-react-native": "7.0.0", "@craftzdog/pouchdb-replication-react-native": "^7.0.0", "@gorhom/bottom-sheet": "^4", "@mwg-sdk/metric": "git+https://sourceapp.tgdd.vn/appplugins/react-native-mwg-metric.git#yarnV4", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-community/async-storage": "^1.12.1", "@react-native-community/checkbox": "^0.5.7", "@react-native-community/datetimepicker": "^3.4.3", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "5.9.10", "@react-native-community/push-notification-ios": "aasatech/push-notification-ios", "@react-native-community/toolbar-android": "^0.2.1", "@react-native-firebase/analytics": "12.9.3", "@react-native-firebase/app": "^12.9.3", "@react-native-firebase/messaging": "^12.9.3", "@react-navigation/bottom-tabs": "^6.5.9", "@react-navigation/drawer": "^6.6.4", "@react-navigation/material-bottom-tabs": "^6.2.17", "@react-navigation/material-top-tabs": "^6.6.10", "@react-navigation/native": "^6.1.8", "@react-navigation/native-stack": "^6.9.14", "@react-navigation/stack": "^6.3.18", "abortcontroller-polyfill": "^1.3.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-react-native": "^4.0.1", "base-64": "^1.0.0", "dayjs": "^1.10.5", "events": "^3.3.0", "graphql": "^15.5.0", "graphql-normalizr": "^2.10.1", "hypersnapsdk_reactnative": "4.16.1", "i18n-js": "^3.8.0", "jail-monkey": "^2.6.0", "lodash.memoize": "^4.1.2", "lottie-react-native": "^4.0.3", "moment": "^2.29.1", "pouchdb-adapter-http": "7.1.1", "pouchdb-adapter-react-native-sqlite": "2.0.0", "pouchdb-mapreduce": "7.1.1", "pouchdb-upsert": "^2.2.0", "prop-types": "^15.7.2", "react": "18.2.0", "react-native": "0.72.8", "react-native-audio-record": "^0.2.2", "react-native-background-timer": "^2.4.1", "react-native-best-viewpager": "git+https://sourceapp.tgdd.vn/appplugins/react-native-best-viewpager.git#dev_nkda", "react-native-bouncy-checkbox": "^3.0.2", "react-native-calendar-picker": "^7.1.1", "react-native-calendars": "^1.1128.0", "react-native-camera": "git+https://sourceapp.tgdd.vn/appplugins/react-native-camera.git#dev_nkda", "react-native-clear-cache": "git+https://sourceapp.tgdd.vn/appplugins/react-native-clear-cache.git#dev_rn72", "react-native-code-push": "git+https://sourceapp.tgdd.vn/appplugins/react-native-code-push-0.60.git#dev_nkda", "react-native-collapsible": "https://github.com/kidken285/react-native-collapsible.git", "react-native-config": "^1.5.1", "react-native-crypto-js": "git+https://sourceapp.tgdd.vn/appplugins/react-native-crypto-js.git", "react-native-dash": "^0.0.11", "react-native-date-picker": "^4.4.1", "react-native-datepicker": "^1.7.2", "react-native-detector": "git+https://sourceapp.tgdd.vn/app/mwgposlibraries/react-native-detector.git", "react-native-device-info": "git+https://sourceapp.tgdd.vn/appplugins/react-native-device-info.git#dev_nkda", "react-native-dialog": "^9.1.0", "react-native-document-picker": "^3.4.0", "react-native-element-dropdown": "^2.10.0", "react-native-exception-handler": "git+https://sourceapp.tgdd.vn/appplugins/react-native-exception-handler.git#dev_nkda", "react-native-extra-dimensions-android": "^1.2.5", "react-native-fast-image": "8.3.4", "react-native-flash-message": "^0.1.23", "react-native-floating-action": "git+https://sourceapp.tgdd.vn/appplugins/react-native-floating-action.git", "react-native-fs": "^2.18.0", "react-native-gesture-handler": "^1.10.3", "react-native-get-random-values": "^1.8.0", "react-native-html-converter": "git+https://sourceapp.tgdd.vn/appplugins/react-native-html-converter.git#dev_nkda", "react-native-htmlview": "^0.16.0", "react-native-image-marker": "1.1.15", "react-native-image-pan-zoom": "^2.1.11", "react-native-image-picker": "^2.3.1", "react-native-image-resizer": "^1.2.1", "react-native-image-zoom-viewer": "^3.0.1", "react-native-indicators": "^0.17.0", "react-native-input-spinner": "^1.3.2", "react-native-iphone-x-helper": "^1.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.1", "react-native-linear-gradient": "^2.4.2", "react-native-localize": "^2.1.1", "react-native-material-menu": "^2.0.0", "react-native-material-ripple": "^0.9.1", "react-native-modal": "^13.0.1", "react-native-offline": "^5.2.0", "react-native-pager-view": "6.3.0", "react-native-parsed-text": "^0.0.22", "react-native-permissions": "^3.0.4", "react-native-progress": "^5.0.0", "react-native-progress-steps": "^1.3.4", "react-native-push-notification": "^7.4.0", "react-native-qrcode-svg": "^6.3.14", "react-native-reanimated": "3.6.1", "react-native-restart": "^0.0.13", "react-native-safe-area-context": "^3.2.0", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "3.31.1", "react-native-sensitive-info": "^5.5.8", "react-native-signature-canvas": "git+https://sourceapp.tgdd.vn/appplugins/react-native-signature-canvas.git#dev_tptuyen", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-sqlite-2": "^3.4.1", "react-native-static-safe-area-insets": "^2.2.0", "react-native-step-indicator": "^0.0.11", "react-native-svg": "15.2.0", "react-native-tab-view": "^3.5.2", "react-native-tcp-socket": "^5.6.2", "react-native-text-input-mask": "^3.2.0", "react-native-toast-message": "^2.1.5", "react-native-vector-icons": "^8.1.0", "react-native-view-pdf": "^0.12.1", "react-native-vision-camera": "git+https://sourceapp.tgdd.vn/appplugins/react-native-vision-camera-ios-v3.git#dev_nkda", "react-native-walkthrough-tooltip": "^1.1.1", "react-native-webview": "11.23.1", "react-redux": "^7.2.4", "redux": "^4.1.0", "redux-action-listeners": "^1.0.2", "redux-logger": "^3.0.6", "redux-pouchdb": "git+https://sourceapp.tgdd.vn/appplugins/redux-pouchdb.git#dev_tptdong", "redux-recycle": "^1.4.0", "redux-reset": "^0.3.0", "redux-saga": "^1.1.3", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "rn-crypto-js": "^1.1.0", "rn-fetch-blob": "^0.12.0", "utf8": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.13.15", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@ken/eslint-config-javascript-standard-reactnative": "git+https://sourceapp.tgdd.vn/appplugins/eslint-config-javascript-standard-reactnative.git", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}